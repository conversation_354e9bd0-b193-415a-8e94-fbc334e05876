2025-09-04 15:47:15.847 | [34m INFO 70519[0;39m | [1;33mmain[0;39m [1;32mptablePropertiesBeanFactoryPostProcessor[0;39m | Post-processing PropertySource instances
2025-09-04 15:47:15.971 | [34m INFO 70519[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2025-09-04 15:47:15.973 | [34m INFO 70519[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 15:47:15.974 | [34m INFO 70519[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 15:47:15.974 | [34m INFO 70519[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 15:47:15.975 | [34m INFO 70519[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-09-04 15:47:15.975 | [34m INFO 70519[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-09-04 15:47:15.975 | [34m INFO 70519[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 15:47:15.975 | [34m INFO 70519[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 15:47:16.067 | [34m INFO 70519[0;39m | [1;33mmain[0;39m [1;32mc.u.j.filter.DefaultLazyPropertyFilter  [0;39m | Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-09-04 15:47:16.094 | [34m INFO 70519[0;39m | [1;33mmain[0;39m [1;32mc.u.j.r.DefaultLazyPropertyResolver     [0;39m | Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-09-04 15:47:16.096 | [34m INFO 70519[0;39m | [1;33mmain[0;39m [1;32mc.u.j.d.DefaultLazyPropertyDetector     [0;39m | Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-09-04 15:47:16.730 | [34m INFO 70519[0;39m | [1;33mmain[0;39m [1;32mc.a.n.c.c.impl.LocalConfigInfoProcessor [0;39m | LOCAL_SNAPSHOT_PATH:/Users/<USER>/nacos/config
2025-09-04 15:47:16.762 | [34m INFO 70519[0;39m | [1;33mmain[0;39m [1;32mc.a.nacos.client.config.utils.JvmUtil   [0;39m | isMultiInstance:false
2025-09-04 15:47:16.790 | [31m WARN 70519[0;39m | [1;33mmain[0;39m [1;32mc.a.c.n.c.NacosPropertySourceBuilder    [0;39m | Ignore the empty nacos configuration and get it based on dataId[kepler-integration] & group[DEFAULT_GROUP]
2025-09-04 15:47:16.812 | [34m INFO 70519[0;39m | [1;33mmain[0;39m [1;32mb.c.PropertySourceBootstrapConfiguration[0;39m | Located property source: [BootstrapPropertySource {name='bootstrapProperties-kepler-integration.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kepler-integration,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-application.yml,DEFAULT_GROUP'}]
2025-09-04 15:47:16.843 | [34m INFO 70519[0;39m | [1;33mmain[0;39m [1;32mEnableEncryptablePropertiesConfiguration[0;39m | Bootstraping jasypt-string-boot auto configuration in context: kepler-integration-1
2025-09-04 15:47:16.844 | [34m INFO 70519[0;39m | [1;33mmain[0;39m [1;32mc.t.i.KeplerIntegrationServerApplication[0;39m | The following 2 profiles are active: "dev", "uat"
2025-09-04 15:47:18.143 | [1;31mERROR 70519[0;39m | [1;33mmain[0;39m [1;32mo.s.boot.SpringApplication              [0;39m | Application run failed

java.lang.IllegalStateException: Error processing condition on com.trinasolar.tasc.framework.web.config.TsWebAutoConfiguration.restTemplate
	at org.springframework.boot.autoconfigure.condition.SpringBootCondition.matches(SpringBootCondition.java:60)
	at org.springframework.context.annotation.ConditionEvaluator.shouldSkip(ConditionEvaluator.java:108)
	at org.springframework.context.annotation.ConfigurationClassBeanDefinitionReader.loadBeanDefinitionsForBeanMethod(ConfigurationClassBeanDefinitionReader.java:193)
	at org.springframework.context.annotation.ConfigurationClassBeanDefinitionReader.loadBeanDefinitionsForConfigurationClass(ConfigurationClassBeanDefinitionReader.java:153)
	at org.springframework.context.annotation.ConfigurationClassBeanDefinitionReader.loadBeanDefinitions(ConfigurationClassBeanDefinitionReader.java:129)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.processConfigBeanDefinitions(ConfigurationClassPostProcessor.java:343)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.postProcessBeanDefinitionRegistry(ConfigurationClassPostProcessor.java:247)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanDefinitionRegistryPostProcessors(PostProcessorRegistrationDelegate.java:311)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:112)
	at org.springframework.context.support.AbstractApplicationContext.invokeBeanFactoryPostProcessors(AbstractApplicationContext.java:756)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:573)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1289)
	at com.trinasolar.integration.KeplerIntegrationServerApplication.main(KeplerIntegrationServerApplication.java:22)
Caused by: java.lang.IllegalStateException: Failed to introspect Class [com.trinasolar.integration.kafka.config.TsKafkaAutoConfiguration] from ClassLoader [jdk.internal.loader.ClassLoaders$AppClassLoader@9e89d68]
	at org.springframework.util.ReflectionUtils.getDeclaredMethods(ReflectionUtils.java:485)
	at org.springframework.util.ReflectionUtils.doWithMethods(ReflectionUtils.java:361)
	at org.springframework.util.ReflectionUtils.getUniqueDeclaredMethods(ReflectionUtils.java:418)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.lambda$getTypeForFactoryMethod$2(AbstractAutowireCapableBeanFactory.java:765)
	at java.base/java.util.concurrent.ConcurrentHashMap.computeIfAbsent(ConcurrentHashMap.java:1705)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.getTypeForFactoryMethod(AbstractAutowireCapableBeanFactory.java:764)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.determineTargetType(AbstractAutowireCapableBeanFactory.java:703)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.predictBeanType(AbstractAutowireCapableBeanFactory.java:674)
	at org.springframework.beans.factory.support.AbstractBeanFactory.isFactoryBean(AbstractBeanFactory.java:1685)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doGetBeanNamesForType(DefaultListableBeanFactory.java:570)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBeanNamesForType(DefaultListableBeanFactory.java:542)
	at org.springframework.boot.autoconfigure.condition.OnBeanCondition.collectBeanNamesForType(OnBeanCondition.java:246)
	at org.springframework.boot.autoconfigure.condition.OnBeanCondition.getBeanNamesForType(OnBeanCondition.java:239)
	at org.springframework.boot.autoconfigure.condition.OnBeanCondition.getBeanNamesForType(OnBeanCondition.java:229)
	at org.springframework.boot.autoconfigure.condition.OnBeanCondition.getMatchingBeans(OnBeanCondition.java:182)
	at org.springframework.boot.autoconfigure.condition.OnBeanCondition.getMatchOutcome(OnBeanCondition.java:157)
	at org.springframework.boot.autoconfigure.condition.SpringBootCondition.matches(SpringBootCondition.java:47)
	... 17 common frames omitted
Caused by: java.lang.NoClassDefFoundError: KafkaProducerClient
	at java.base/java.lang.Class.getDeclaredMethods0(Native Method)
	at java.base/java.lang.Class.privateGetDeclaredMethods(Class.java:3166)
	at java.base/java.lang.Class.getDeclaredMethods(Class.java:2309)
	at org.springframework.util.ReflectionUtils.getDeclaredMethods(ReflectionUtils.java:467)
	... 33 common frames omitted
Caused by: java.lang.ClassNotFoundException: KafkaProducerClient
	at java.base/jdk.internal.loader.BuiltinClassLoader.loadClass(BuiltinClassLoader.java:581)
	at java.base/jdk.internal.loader.ClassLoaders$AppClassLoader.loadClass(ClassLoaders.java:178)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	... 37 common frames omitted

2025-09-04 15:47:18.146 | [31m WARN 70519[0;39m | [1;33mmain[0;39m [1;32mo.s.boot.SpringApplication              [0;39m | Unable to close ApplicationContext

java.lang.IllegalStateException: Failed to introspect Class [com.trinasolar.integration.kafka.config.TsKafkaAutoConfiguration] from ClassLoader [jdk.internal.loader.ClassLoaders$AppClassLoader@9e89d68]
	at org.springframework.util.ReflectionUtils.getDeclaredMethods(ReflectionUtils.java:485)
	at org.springframework.util.ReflectionUtils.doWithMethods(ReflectionUtils.java:361)
	at org.springframework.util.ReflectionUtils.getUniqueDeclaredMethods(ReflectionUtils.java:418)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.lambda$getTypeForFactoryMethod$2(AbstractAutowireCapableBeanFactory.java:765)
	at java.base/java.util.concurrent.ConcurrentHashMap.computeIfAbsent(ConcurrentHashMap.java:1705)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.getTypeForFactoryMethod(AbstractAutowireCapableBeanFactory.java:764)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.determineTargetType(AbstractAutowireCapableBeanFactory.java:703)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.predictBeanType(AbstractAutowireCapableBeanFactory.java:674)
	at org.springframework.beans.factory.support.AbstractBeanFactory.isFactoryBean(AbstractBeanFactory.java:1685)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doGetBeanNamesForType(DefaultListableBeanFactory.java:570)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBeanNamesForType(DefaultListableBeanFactory.java:542)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBeansOfType(DefaultListableBeanFactory.java:669)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBeansOfType(DefaultListableBeanFactory.java:661)
	at org.springframework.context.support.AbstractApplicationContext.getBeansOfType(AbstractApplicationContext.java:1317)
	at org.springframework.boot.SpringApplication.getExitCodeFromMappedException(SpringApplication.java:861)
	at org.springframework.boot.SpringApplication.getExitCodeFromException(SpringApplication.java:849)
	at org.springframework.boot.SpringApplication.handleExitCode(SpringApplication.java:836)
	at org.springframework.boot.SpringApplication.handleRunFailure(SpringApplication.java:776)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1289)
	at com.trinasolar.integration.KeplerIntegrationServerApplication.main(KeplerIntegrationServerApplication.java:22)
Caused by: java.lang.NoClassDefFoundError: KafkaProducerClient
	at java.base/java.lang.Class.getDeclaredMethods0(Native Method)
	at java.base/java.lang.Class.privateGetDeclaredMethods(Class.java:3166)
	at java.base/java.lang.Class.getDeclaredMethods(Class.java:2309)
	at org.springframework.util.ReflectionUtils.getDeclaredMethods(ReflectionUtils.java:467)
	... 21 common frames omitted
Caused by: java.lang.ClassNotFoundException: KafkaProducerClient
	at java.base/jdk.internal.loader.BuiltinClassLoader.loadClass(BuiltinClassLoader.java:581)
	at java.base/jdk.internal.loader.ClassLoaders$AppClassLoader.loadClass(ClassLoaders.java:178)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	... 25 common frames omitted

2025-09-04 15:47:18.147 | [31m WARN 70519[0;39m | [1;33mThread-2[0;39m [1;32mc.a.n.common.http.HttpClientBeanHolder  [0;39m | [HttpClientBeanHolder] Start destroying common HttpClient
2025-09-04 15:48:22.530 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mptablePropertiesBeanFactoryPostProcessor[0;39m | Post-processing PropertySource instances
2025-09-04 15:48:22.768 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2025-09-04 15:48:22.771 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 15:48:22.772 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 15:48:22.772 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 15:48:22.773 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-09-04 15:48:22.773 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-09-04 15:48:22.773 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 15:48:22.773 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 15:48:22.882 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mc.u.j.filter.DefaultLazyPropertyFilter  [0;39m | Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-09-04 15:48:22.886 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mc.u.j.r.DefaultLazyPropertyResolver     [0;39m | Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-09-04 15:48:22.888 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mc.u.j.d.DefaultLazyPropertyDetector     [0;39m | Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-09-04 15:48:23.473 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mc.a.n.c.c.impl.LocalConfigInfoProcessor [0;39m | LOCAL_SNAPSHOT_PATH:/Users/<USER>/nacos/config
2025-09-04 15:48:23.513 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mc.a.nacos.client.config.utils.JvmUtil   [0;39m | isMultiInstance:false
2025-09-04 15:48:23.542 | [31m WARN 70776[0;39m | [1;33mmain[0;39m [1;32mc.a.c.n.c.NacosPropertySourceBuilder    [0;39m | Ignore the empty nacos configuration and get it based on dataId[kepler-integration] & group[DEFAULT_GROUP]
2025-09-04 15:48:23.562 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mb.c.PropertySourceBootstrapConfiguration[0;39m | Located property source: [BootstrapPropertySource {name='bootstrapProperties-kepler-integration.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kepler-integration,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-application.yml,DEFAULT_GROUP'}]
2025-09-04 15:48:23.593 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mEnableEncryptablePropertiesConfiguration[0;39m | Bootstraping jasypt-string-boot auto configuration in context: kepler-integration-1
2025-09-04 15:48:23.594 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mc.t.i.KeplerIntegrationServerApplication[0;39m | The following 2 profiles are active: "dev", "uat"
2025-09-04 15:48:24.944 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32m.s.d.r.c.RepositoryConfigurationDelegate[0;39m | Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-04 15:48:24.948 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32m.s.d.r.c.RepositoryConfigurationDelegate[0;39m | Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-04 15:48:25.009 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32m.s.d.r.c.RepositoryConfigurationDelegate[0;39m | Finished Spring Data repository scanning in 38 ms. Found 0 Redis repository interfaces.
2025-09-04 15:48:25.283 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mo.s.cloud.context.scope.GenericScope    [0;39m | BeanFactory id=9acac4ac-0ac4-3e02-bedb-10d1a473c645
2025-09-04 15:48:25.338 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mptablePropertiesBeanFactoryPostProcessor[0;39m | Post-processing PropertySource instances
2025-09-04 15:48:25.350 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrapProperties-kepler-integration.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-09-04 15:48:25.350 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrapProperties-kepler-integration,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-09-04 15:48:25.350 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrapProperties-application.yml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-09-04 15:48:25.350 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2025-09-04 15:48:25.350 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-09-04 15:48:25.351 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-09-04 15:48:25.351 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 15:48:25.351 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 15:48:25.351 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-09-04 15:48:25.351 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-09-04 15:48:25.351 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 15:48:25.351 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 15:48:25.360 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mc.u.j.filter.DefaultLazyPropertyFilter  [0;39m | Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-09-04 15:48:25.368 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mc.u.j.r.DefaultLazyPropertyResolver     [0;39m | Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-09-04 15:48:25.368 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mc.u.j.d.DefaultLazyPropertyDetector     [0;39m | Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-09-04 15:48:25.532 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-04 15:48:25.536 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-04 15:48:25.537 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$663/0x00000008005a7040] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-04 15:48:25.539 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-04 15:48:26.009 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m | Tomcat initialized with port(s): 80 (http)
2025-09-04 15:48:26.020 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mo.apache.catalina.core.StandardService  [0;39m | Starting service [Tomcat]
2025-09-04 15:48:26.021 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32morg.apache.catalina.core.StandardEngine [0;39m | Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-09-04 15:48:26.201 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mo.a.c.c.C.[.[.[/kepler/integration]     [0;39m | Initializing Spring embedded WebApplicationContext
2025-09-04 15:48:26.202 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mw.s.c.ServletWebServerApplicationContext[0;39m | Root WebApplicationContext: initialization completed in 2579 ms
2025-09-04 15:48:26.495 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mf.a.AutowiredAnnotationBeanPostProcessor[0;39m | Autowired annotation is not supported on static fields: private static java.lang.String com.trinasolar.integration.config.security.aes.AESConfig.key
2025-09-04 15:48:26.560 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m | mybatis plus join properties config complete
2025-09-04 15:48:26.995 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1,master} inited
2025-09-04 15:48:26.996 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource - add a datasource named [master] success
2025-09-04 15:48:26.996 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-09-04 15:48:27.059 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m | mybatis plus join SqlInjector init
2025-09-04 15:48:29.245 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-09-04 15:48:29.252 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
2025-09-04 15:48:29.253 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.keyObtentionIterations, using default value: 1000
2025-09-04 15:48:29.253 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.poolSize, using default value: 1
2025-09-04 15:48:29.253 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.providerName, using default value: null
2025-09-04 15:48:29.253 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.providerClassName, using default value: null
2025-09-04 15:48:29.253 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.saltGeneratorClassname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-09-04 15:48:29.254 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.ivGeneratorClassname, using default value: org.jasypt.salt.NoOpIVGenerator
2025-09-04 15:48:29.254 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.stringOutputType, using default value: base64
2025-09-04 15:48:29.508 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32morg.redisson.Version                    [0;39m | Redisson 3.41.0
2025-09-04 15:48:29.589 | [31m WARN 70776[0;39m | [1;33mmain[0;39m [1;32mi.n.r.d.DnsServerAddressStreamProviders [0;39m | Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-09-04 15:48:29.862 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | Redis cluster nodes configuration got from ***********/***********:30012:
461896f00a0c24e3126dc34638854ff6422fb244 ***********:30012@30018 myself,master - 0 1756972109000 1 connected 0-5460
e0b782a8e69537dcc37ea3d2a72258a45ddb2d53 ***********:30014@30020 master - 0 1756972110000 3 connected 10923-16383
93167ed470a2aaf64573d363e6bd6554e03bc4d9 ***********:30013@30019 master - 0 1756972108318 2 connected 5461-10922
0d5fd0961bdeb1f6249808fe9218ccbf5707f630 ***********:30017@30023 slave 461896f00a0c24e3126dc34638854ff6422fb244 0 1756972110328 1 connected
7814d1a5f8f3a16c9b5a1a4212329b85b44e2027 ***********:30015@30021 slave 93167ed470a2aaf64573d363e6bd6554e03bc4d9 0 1756972111331 2 connected
19e5162fb0b4f5bd64d046f5a9a2c2b1db62918e ***********:30016@30022 slave e0b782a8e69537dcc37ea3d2a72258a45ddb2d53 0 1756972109324 3 connected

2025-09-04 15:48:29.921 | [34m INFO 70776[0;39m | [1;33mredisson-netty-1-18[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30012
2025-09-04 15:48:29.921 | [34m INFO 70776[0;39m | [1;33mredisson-netty-1-19[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30014
2025-09-04 15:48:29.943 | [34m INFO 70776[0;39m | [1;33mredisson-netty-1-26[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30013
2025-09-04 15:48:30.261 | [34m INFO 70776[0;39m | [1;33mredisson-netty-1-26[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30014
2025-09-04 15:48:30.261 | [34m INFO 70776[0;39m | [1;33mredisson-netty-1-25[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30012
2025-09-04 15:48:30.271 | [34m INFO 70776[0;39m | [1;33mredisson-netty-1-2[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30013
2025-09-04 15:48:30.308 | [34m INFO 70776[0;39m | [1;33mredisson-netty-1-14[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30017
2025-09-04 15:48:30.308 | [34m INFO 70776[0;39m | [1;33mredisson-netty-1-32[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30016
2025-09-04 15:48:30.308 | [34m INFO 70776[0;39m | [1;33mredisson-netty-1-15[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30015
2025-09-04 15:48:30.618 | [34m INFO 70776[0;39m | [1;33mredisson-netty-1-20[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30017
2025-09-04 15:48:30.619 | [34m INFO 70776[0;39m | [1;33mredisson-netty-1-20[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | slaves: [redis://***********:30017] added for master: redis://***********:30012 slot ranges: [[0-5460]]
2025-09-04 15:48:30.619 | [34m INFO 70776[0;39m | [1;33mredisson-netty-1-20[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | master: redis://***********:30012 added for slot ranges: [[0-5460]]
2025-09-04 15:48:30.626 | [34m INFO 70776[0;39m | [1;33mredisson-netty-1-21[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30016
2025-09-04 15:48:30.626 | [34m INFO 70776[0;39m | [1;33mredisson-netty-1-22[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30015
2025-09-04 15:48:30.627 | [34m INFO 70776[0;39m | [1;33mredisson-netty-1-21[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | slaves: [redis://***********:30016] added for master: redis://***********:30014 slot ranges: [[10923-16383]]
2025-09-04 15:48:30.627 | [34m INFO 70776[0;39m | [1;33mredisson-netty-1-22[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | slaves: [redis://***********:30015] added for master: redis://***********:30013 slot ranges: [[5461-10922]]
2025-09-04 15:48:30.627 | [34m INFO 70776[0;39m | [1;33mredisson-netty-1-21[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | master: redis://***********:30014 added for slot ranges: [[10923-16383]]
2025-09-04 15:48:30.627 | [34m INFO 70776[0;39m | [1;33mredisson-netty-1-22[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | master: redis://***********:30013 added for slot ranges: [[5461-10922]]
2025-09-04 15:48:31.746 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mc.t.t.f.j.c.TsJacksonAutoConfiguration  [0;39m | [init][初始化 JsonUtils 成功]
2025-09-04 15:48:32.174 | [31m WARN 70776[0;39m | [1;33mmain[0;39m [1;32miguration$LoadBalancerCaffeineWarnLogger[0;39m | Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-09-04 15:48:32.355 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | initializer namespace from System Property :null
2025-09-04 15:48:32.356 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | initializer namespace from System Environment :null
2025-09-04 15:48:32.357 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | initializer namespace from System Property :null
2025-09-04 15:48:32.577 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | new ips(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
2025-09-04 15:48:32.585 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | current ips:(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
2025-09-04 15:48:32.608 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m | Tomcat started on port(s): 80 (http) with context path '/kepler/integration'
2025-09-04 15:48:32.614 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [BEAT] adding beat: BeatInfo{port=80, ip='************', weight=1.0, serviceName='DEFAULT_GROUP@@kepler-integration', cluster='DEFAULT', metadata={preserved.heart.beat.timeout=15000, preserved.ip.delete.timeout=30000, preserved.register.source=SPRING_CLOUD, preserved.heart.beat.interval=5000}, scheduled=false, period=5000, stopped=false} to beat map.
2025-09-04 15:48:32.615 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [REGISTER-SERVICE] dev registering service DEFAULT_GROUP@@kepler-integration with instance: Instance{instanceId='null', ip='************', port=80, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.heart.beat.timeout=15000, preserved.ip.delete.timeout=30000, preserved.register.source=SPRING_CLOUD, preserved.heart.beat.interval=5000}}
2025-09-04 15:48:32.622 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mc.a.c.n.registry.NacosServiceRegistry   [0;39m | nacos registry, DEFAULT_GROUP kepler-integration ************:80 register finished
2025-09-04 15:48:32.645 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mc.t.i.KeplerIntegrationServerApplication[0;39m | Started KeplerIntegrationServerApplication in 11.961 seconds (JVM running for 13.548)
2025-09-04 15:48:32.646 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 开始执行数据初始化任务...
2025-09-04 15:48:32.687 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 获取到初始化锁，开始执行数据初始化...
2025-09-04 15:48:32.687 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 开始检查应用系统变更表初始化状态...
2025-09-04 15:48:32.871 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 查询到应用系统数据 73 条，开始全量对比变更表...
2025-09-04 15:48:32.959 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 变更表中已存在版本为1的记录 0 条
2025-09-04 15:48:32.996 | [1;31mERROR 70776[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 数据初始化任务执行失败

java.lang.IllegalArgumentException: Java 8 date/time type `java.time.LocalDateTime` not supported by default: add Module "com.fasterxml.jackson.datatype:jackson-datatype-jsr310" to enable handling (through reference chain: com.trinasolar.integration.api.entity.AppSystem["createdTime"])
	at com.fasterxml.jackson.databind.ObjectMapper.valueToTree(ObjectMapper.java:3394)
	at com.trinasolar.integration.task.DataInitializationTask.initializeAppSystemChangeTable(DataInitializationTask.java:156)
	at com.trinasolar.integration.task.DataInitializationTask.run(DataInitializationTask.java:79)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:756)
	at org.springframework.boot.SpringApplication.lambda$callRunners$2(SpringApplication.java:746)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:183)
	at java.base/java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:357)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:485)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:474)
	at java.base/java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:150)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:173)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:497)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:744)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1289)
	at com.trinasolar.integration.KeplerIntegrationServerApplication.main(KeplerIntegrationServerApplication.java:22)
Caused by: com.fasterxml.jackson.databind.exc.InvalidDefinitionException: Java 8 date/time type `java.time.LocalDateTime` not supported by default: add Module "com.fasterxml.jackson.datatype:jackson-datatype-jsr310" to enable handling (through reference chain: com.trinasolar.integration.api.entity.AppSystem["createdTime"])
	at com.fasterxml.jackson.databind.exc.InvalidDefinitionException.from(InvalidDefinitionException.java:77)
	at com.fasterxml.jackson.databind.SerializerProvider.reportBadDefinition(SerializerProvider.java:1300)
	at com.fasterxml.jackson.databind.ser.impl.UnsupportedTypeSerializer.serialize(UnsupportedTypeSerializer.java:35)
	at com.fasterxml.jackson.databind.ser.BeanPropertyWriter.serializeAsField(BeanPropertyWriter.java:728)
	at com.fasterxml.jackson.databind.ser.std.BeanSerializerBase.serializeFields(BeanSerializerBase.java:774)
	at com.fasterxml.jackson.databind.ser.BeanSerializer.serialize(BeanSerializer.java:178)
	at com.fasterxml.jackson.databind.ser.DefaultSerializerProvider._serialize(DefaultSerializerProvider.java:480)
	at com.fasterxml.jackson.databind.ser.DefaultSerializerProvider.serializeValue(DefaultSerializerProvider.java:319)
	at com.fasterxml.jackson.databind.ObjectMapper.valueToTree(ObjectMapper.java:3389)
	... 17 common frames omitted

2025-09-04 15:48:33.014 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 释放初始化锁
2025-09-04 15:48:33.030 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mc.a.n.client.config.impl.ClientWorker   [0;39m | [fixed-10.180.72.6_8848-dev] [subscribe] kepler-integration+DEFAULT_GROUP+dev
2025-09-04 15:48:33.032 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mc.a.nacos.client.config.impl.CacheData  [0;39m | [fixed-10.180.72.6_8848-dev] [add-listener] ok, tenant=dev, dataId=kepler-integration, group=DEFAULT_GROUP, cnt=1
2025-09-04 15:48:33.032 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mc.a.n.client.config.impl.ClientWorker   [0;39m | [fixed-10.180.72.6_8848-dev] [subscribe] kepler-integration.yaml+DEFAULT_GROUP+dev
2025-09-04 15:48:33.032 | [34m INFO 70776[0;39m | [1;33mmain[0;39m [1;32mc.a.nacos.client.config.impl.CacheData  [0;39m | [fixed-10.180.72.6_8848-dev] [add-listener] ok, tenant=dev, dataId=kepler-integration.yaml, group=DEFAULT_GROUP, cnt=1
2025-09-04 15:48:33.605 | [34m INFO 70776[0;39m | [1;33mcom.alibaba.nacos.client.naming.updater[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | new ips(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
2025-09-04 15:48:33.607 | [34m INFO 70776[0;39m | [1;33mcom.alibaba.nacos.client.naming.updater[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | current ips:(2) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000},{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
2025-09-04 15:49:00.018 | [34m INFO 70776[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.task.ScaScanDistributedTask       [0;39m | =======执行的SCA状态扫描任务======
2025-09-04 15:49:15.062 | [31m WARN 70776[0;39m | [1;33mThread-2[0;39m [1;32mc.a.n.common.http.HttpClientBeanHolder  [0;39m | [HttpClientBeanHolder] Start destroying common HttpClient
2025-09-04 15:49:15.062 | [31m WARN 70776[0;39m | [1;33mThread-16[0;39m [1;32mc.a.nacos.common.notify.NotifyCenter    [0;39m | [NotifyCenter] Start destroying Publisher
2025-09-04 15:49:15.062 | [31m WARN 70776[0;39m | [1;33mThread-16[0;39m [1;32mc.a.nacos.common.notify.NotifyCenter    [0;39m | [NotifyCenter] Destruction of the end
2025-09-04 15:49:15.063 | [31m WARN 70776[0;39m | [1;33mThread-2[0;39m [1;32mc.a.n.common.http.HttpClientBeanHolder  [0;39m | [HttpClientBeanHolder] Destruction of the end
2025-09-04 15:49:15.091 | [34m INFO 70776[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.a.c.n.registry.NacosServiceRegistry   [0;39m | De-registering from Nacos Server now...
2025-09-04 15:49:15.092 | [34m INFO 70776[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [BEAT] removing beat: DEFAULT_GROUP@@kepler-integration:************:80 from beat map.
2025-09-04 15:49:15.092 | [34m INFO 70776[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [DEREGISTER-SERVICE] dev deregistering service DEFAULT_GROUP@@kepler-integration with instance: Instance{instanceId='null', ip='************', port=80, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={}}
2025-09-04 15:49:15.099 | [34m INFO 70776[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.a.c.n.registry.NacosServiceRegistry   [0;39m | De-registration finished.
2025-09-04 15:49:15.099 | [34m INFO 70776[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown begin
2025-09-04 15:49:17.803 | [34m INFO 70776[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown stop
2025-09-04 15:49:17.804 | [34m INFO 70776[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.core.HostReactor do shutdown begin
2025-09-04 15:49:20.811 | [34m INFO 70776[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.core.PushReceiver do shutdown begin
2025-09-04 15:49:23.817 | [34m INFO 70776[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.core.PushReceiver do shutdown stop
2025-09-04 15:49:23.818 | [34m INFO 70776[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin
2025-09-04 15:49:23.818 | [34m INFO 70776[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop
2025-09-04 15:49:23.818 | [34m INFO 70776[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.core.HostReactor do shutdown stop
2025-09-04 15:49:23.818 | [34m INFO 70776[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.net.NamingProxy do shutdown begin
2025-09-04 15:49:23.818 | [31m WARN 70776[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [NamingHttpClientManager] Start destroying NacosRestTemplate
2025-09-04 15:49:23.818 | [31m WARN 70776[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [NamingHttpClientManager] Destruction of the end
2025-09-04 15:49:23.819 | [34m INFO 70776[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.a.n.client.identify.CredentialWatcher [0;39m | [null] CredentialWatcher is stopped
2025-09-04 15:49:23.819 | [34m INFO 70776[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.a.n.client.identify.CredentialService [0;39m | [null] CredentialService is freed
2025-09-04 15:49:23.819 | [34m INFO 70776[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.net.NamingProxy do shutdown stop
2025-09-04 15:49:23.819 | [31m WARN 70776[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mo.s.b.f.support.DisposableBeanAdapter   [0;39m | Custom destroy method 'close' on bean with name 'nacosServiceRegistry' threw an exception: java.lang.NullPointerException
2025-09-04 15:49:23.887 | [34m INFO 70776[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource start closing ....
2025-09-04 15:49:23.890 | [34m INFO 70776[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1} closing ...
2025-09-04 15:49:23.907 | [34m INFO 70776[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1} closed
2025-09-04 15:49:23.907 | [34m INFO 70776[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.b.d.d.d.DefaultDataSourceDestroyer    [0;39m | dynamic-datasource close the datasource named [master] success,
2025-09-04 15:49:23.907 | [34m INFO 70776[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource all closed success,bye
2025-09-04 15:50:08.248 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mptablePropertiesBeanFactoryPostProcessor[0;39m | Post-processing PropertySource instances
2025-09-04 15:50:08.330 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2025-09-04 15:50:08.332 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 15:50:08.332 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 15:50:08.332 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 15:50:08.332 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-09-04 15:50:08.333 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-09-04 15:50:08.333 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 15:50:08.333 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 15:50:08.385 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mc.u.j.filter.DefaultLazyPropertyFilter  [0;39m | Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-09-04 15:50:08.390 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mc.u.j.r.DefaultLazyPropertyResolver     [0;39m | Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-09-04 15:50:08.392 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mc.u.j.d.DefaultLazyPropertyDetector     [0;39m | Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-09-04 15:50:08.946 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mc.a.n.c.c.impl.LocalConfigInfoProcessor [0;39m | LOCAL_SNAPSHOT_PATH:/Users/<USER>/nacos/config
2025-09-04 15:50:09.038 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mc.a.nacos.client.config.utils.JvmUtil   [0;39m | isMultiInstance:false
2025-09-04 15:50:09.069 | [31m WARN 71225[0;39m | [1;33mmain[0;39m [1;32mc.a.c.n.c.NacosPropertySourceBuilder    [0;39m | Ignore the empty nacos configuration and get it based on dataId[kepler-integration] & group[DEFAULT_GROUP]
2025-09-04 15:50:09.087 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mb.c.PropertySourceBootstrapConfiguration[0;39m | Located property source: [BootstrapPropertySource {name='bootstrapProperties-kepler-integration.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kepler-integration,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-application.yml,DEFAULT_GROUP'}]
2025-09-04 15:50:09.127 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mEnableEncryptablePropertiesConfiguration[0;39m | Bootstraping jasypt-string-boot auto configuration in context: kepler-integration-1
2025-09-04 15:50:09.128 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mc.t.i.KeplerIntegrationServerApplication[0;39m | The following 2 profiles are active: "dev", "uat"
2025-09-04 15:50:10.562 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32m.s.d.r.c.RepositoryConfigurationDelegate[0;39m | Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-04 15:50:10.565 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32m.s.d.r.c.RepositoryConfigurationDelegate[0;39m | Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-04 15:50:10.637 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32m.s.d.r.c.RepositoryConfigurationDelegate[0;39m | Finished Spring Data repository scanning in 46 ms. Found 0 Redis repository interfaces.
2025-09-04 15:50:10.929 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mo.s.cloud.context.scope.GenericScope    [0;39m | BeanFactory id=f8c5ab0f-68e8-3840-9720-c2a4515bce20
2025-09-04 15:50:10.985 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mptablePropertiesBeanFactoryPostProcessor[0;39m | Post-processing PropertySource instances
2025-09-04 15:50:10.998 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrapProperties-kepler-integration.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-09-04 15:50:10.998 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrapProperties-kepler-integration,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-09-04 15:50:10.998 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrapProperties-application.yml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-09-04 15:50:10.999 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2025-09-04 15:50:10.999 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-09-04 15:50:10.999 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-09-04 15:50:10.999 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 15:50:10.999 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 15:50:10.999 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-09-04 15:50:10.999 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-09-04 15:50:10.999 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 15:50:10.999 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 15:50:11.007 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mc.u.j.filter.DefaultLazyPropertyFilter  [0;39m | Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-09-04 15:50:11.014 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mc.u.j.r.DefaultLazyPropertyResolver     [0;39m | Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-09-04 15:50:11.014 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mc.u.j.d.DefaultLazyPropertyDetector     [0;39m | Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-09-04 15:50:11.194 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-04 15:50:11.197 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-04 15:50:11.198 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$662/0x00000008005a7440] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-04 15:50:11.199 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-04 15:50:11.663 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m | Tomcat initialized with port(s): 80 (http)
2025-09-04 15:50:11.673 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mo.apache.catalina.core.StandardService  [0;39m | Starting service [Tomcat]
2025-09-04 15:50:11.674 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32morg.apache.catalina.core.StandardEngine [0;39m | Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-09-04 15:50:11.807 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mo.a.c.c.C.[.[.[/kepler/integration]     [0;39m | Initializing Spring embedded WebApplicationContext
2025-09-04 15:50:11.807 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mw.s.c.ServletWebServerApplicationContext[0;39m | Root WebApplicationContext: initialization completed in 2645 ms
2025-09-04 15:50:12.099 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mf.a.AutowiredAnnotationBeanPostProcessor[0;39m | Autowired annotation is not supported on static fields: private static java.lang.String com.trinasolar.integration.config.security.aes.AESConfig.key
2025-09-04 15:50:12.170 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m | mybatis plus join properties config complete
2025-09-04 15:50:12.581 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1,master} inited
2025-09-04 15:50:12.581 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource - add a datasource named [master] success
2025-09-04 15:50:12.582 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-09-04 15:50:12.633 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m | mybatis plus join SqlInjector init
2025-09-04 15:50:14.786 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-09-04 15:50:14.831 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
2025-09-04 15:50:14.831 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.keyObtentionIterations, using default value: 1000
2025-09-04 15:50:14.831 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.poolSize, using default value: 1
2025-09-04 15:50:14.832 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.providerName, using default value: null
2025-09-04 15:50:14.832 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.providerClassName, using default value: null
2025-09-04 15:50:14.832 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.saltGeneratorClassname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-09-04 15:50:14.835 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.ivGeneratorClassname, using default value: org.jasypt.salt.NoOpIVGenerator
2025-09-04 15:50:14.836 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.stringOutputType, using default value: base64
2025-09-04 15:50:15.117 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32morg.redisson.Version                    [0;39m | Redisson 3.41.0
2025-09-04 15:50:15.199 | [31m WARN 71225[0;39m | [1;33mmain[0;39m [1;32mi.n.r.d.DnsServerAddressStreamProviders [0;39m | Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-09-04 15:50:15.512 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | Redis cluster nodes configuration got from ***********/***********:30012:
461896f00a0c24e3126dc34638854ff6422fb244 ***********:30012@30018 myself,master - 0 1756972216000 1 connected 0-5460
e0b782a8e69537dcc37ea3d2a72258a45ddb2d53 ***********:30014@30020 master - 0 1756972215000 3 connected 10923-16383
93167ed470a2aaf64573d363e6bd6554e03bc4d9 ***********:30013@30019 master - 0 1756972216000 2 connected 5461-10922
0d5fd0961bdeb1f6249808fe9218ccbf5707f630 ***********:30017@30023 slave 461896f00a0c24e3126dc34638854ff6422fb244 0 1756972217000 1 connected
7814d1a5f8f3a16c9b5a1a4212329b85b44e2027 ***********:30015@30021 slave 93167ed470a2aaf64573d363e6bd6554e03bc4d9 0 1756972214752 2 connected
19e5162fb0b4f5bd64d046f5a9a2c2b1db62918e ***********:30016@30022 slave e0b782a8e69537dcc37ea3d2a72258a45ddb2d53 0 1756972217764 3 connected

2025-09-04 15:50:15.567 | [34m INFO 71225[0;39m | [1;33mredisson-netty-1-18[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30014
2025-09-04 15:50:15.577 | [34m INFO 71225[0;39m | [1;33mredisson-netty-1-19[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30012
2025-09-04 15:50:15.589 | [34m INFO 71225[0;39m | [1;33mredisson-netty-1-26[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30013
2025-09-04 15:50:15.937 | [34m INFO 71225[0;39m | [1;33mredisson-netty-1-25[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30012
2025-09-04 15:50:15.942 | [34m INFO 71225[0;39m | [1;33mredisson-netty-1-26[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30014
2025-09-04 15:50:15.951 | [34m INFO 71225[0;39m | [1;33mredisson-netty-1-2[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30013
2025-09-04 15:50:15.982 | [34m INFO 71225[0;39m | [1;33mredisson-netty-1-15[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30017
2025-09-04 15:50:15.982 | [34m INFO 71225[0;39m | [1;33mredisson-netty-1-14[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30016
2025-09-04 15:50:15.982 | [34m INFO 71225[0;39m | [1;33mredisson-netty-1-32[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30015
2025-09-04 15:50:16.313 | [34m INFO 71225[0;39m | [1;33mredisson-netty-1-20[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30016
2025-09-04 15:50:16.314 | [34m INFO 71225[0;39m | [1;33mredisson-netty-1-20[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | slaves: [redis://***********:30016] added for master: redis://***********:30014 slot ranges: [[10923-16383]]
2025-09-04 15:50:16.315 | [34m INFO 71225[0;39m | [1;33mredisson-netty-1-20[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | master: redis://***********:30014 added for slot ranges: [[10923-16383]]
2025-09-04 15:50:16.316 | [34m INFO 71225[0;39m | [1;33mredisson-netty-1-21[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30015
2025-09-04 15:50:16.317 | [34m INFO 71225[0;39m | [1;33mredisson-netty-1-21[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | slaves: [redis://***********:30015] added for master: redis://***********:30013 slot ranges: [[5461-10922]]
2025-09-04 15:50:16.317 | [34m INFO 71225[0;39m | [1;33mredisson-netty-1-21[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | master: redis://***********:30013 added for slot ranges: [[5461-10922]]
2025-09-04 15:50:16.320 | [34m INFO 71225[0;39m | [1;33mredisson-netty-1-22[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30017
2025-09-04 15:50:16.320 | [34m INFO 71225[0;39m | [1;33mredisson-netty-1-22[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | slaves: [redis://***********:30017] added for master: redis://***********:30012 slot ranges: [[0-5460]]
2025-09-04 15:50:16.320 | [34m INFO 71225[0;39m | [1;33mredisson-netty-1-22[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | master: redis://***********:30012 added for slot ranges: [[0-5460]]
2025-09-04 15:50:17.353 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mc.t.t.f.j.c.TsJacksonAutoConfiguration  [0;39m | [init][初始化 JsonUtils 成功]
2025-09-04 15:50:17.778 | [31m WARN 71225[0;39m | [1;33mmain[0;39m [1;32miguration$LoadBalancerCaffeineWarnLogger[0;39m | Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-09-04 15:50:17.947 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | initializer namespace from System Property :null
2025-09-04 15:50:17.948 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | initializer namespace from System Environment :null
2025-09-04 15:50:17.948 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | initializer namespace from System Property :null
2025-09-04 15:50:18.195 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | new ips(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-09-04 15:50:18.203 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | current ips:(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-09-04 15:50:18.223 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m | Tomcat started on port(s): 80 (http) with context path '/kepler/integration'
2025-09-04 15:50:18.227 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [BEAT] adding beat: BeatInfo{port=80, ip='************', weight=1.0, serviceName='DEFAULT_GROUP@@kepler-integration', cluster='DEFAULT', metadata={preserved.heart.beat.timeout=15000, preserved.ip.delete.timeout=30000, preserved.register.source=SPRING_CLOUD, preserved.heart.beat.interval=5000}, scheduled=false, period=5000, stopped=false} to beat map.
2025-09-04 15:50:18.228 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [REGISTER-SERVICE] dev registering service DEFAULT_GROUP@@kepler-integration with instance: Instance{instanceId='null', ip='************', port=80, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.heart.beat.timeout=15000, preserved.ip.delete.timeout=30000, preserved.register.source=SPRING_CLOUD, preserved.heart.beat.interval=5000}}
2025-09-04 15:50:18.237 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mc.a.c.n.registry.NacosServiceRegistry   [0;39m | nacos registry, DEFAULT_GROUP kepler-integration ************:80 register finished
2025-09-04 15:50:18.265 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mc.t.i.KeplerIntegrationServerApplication[0;39m | Started KeplerIntegrationServerApplication in 11.34 seconds (JVM running for 13.234)
2025-09-04 15:50:18.266 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 开始执行数据初始化任务...
2025-09-04 15:50:18.305 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 获取到初始化锁，开始执行数据初始化...
2025-09-04 15:50:18.305 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 开始检查应用系统变更表初始化状态...
2025-09-04 15:50:18.454 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 查询到应用系统数据 73 条，开始全量对比变更表...
2025-09-04 15:50:18.474 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 变更表中已存在版本为1的记录 0 条
2025-09-04 15:50:18.499 | [1;31mERROR 71225[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 数据初始化任务执行失败

java.lang.IllegalArgumentException: Java 8 date/time type `java.time.LocalDateTime` not supported by default: add Module "com.fasterxml.jackson.datatype:jackson-datatype-jsr310" to enable handling (through reference chain: com.trinasolar.integration.api.entity.AppSystem["createdTime"])
	at com.fasterxml.jackson.databind.ObjectMapper.valueToTree(ObjectMapper.java:3394)
	at com.trinasolar.integration.task.DataInitializationTask.initializeAppSystemChangeTable(DataInitializationTask.java:156)
	at com.trinasolar.integration.task.DataInitializationTask.run(DataInitializationTask.java:79)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:756)
	at org.springframework.boot.SpringApplication.lambda$callRunners$2(SpringApplication.java:746)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:183)
	at java.base/java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:357)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:485)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:474)
	at java.base/java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:150)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:173)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:497)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:744)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1289)
	at com.trinasolar.integration.KeplerIntegrationServerApplication.main(KeplerIntegrationServerApplication.java:22)
Caused by: com.fasterxml.jackson.databind.exc.InvalidDefinitionException: Java 8 date/time type `java.time.LocalDateTime` not supported by default: add Module "com.fasterxml.jackson.datatype:jackson-datatype-jsr310" to enable handling (through reference chain: com.trinasolar.integration.api.entity.AppSystem["createdTime"])
	at com.fasterxml.jackson.databind.exc.InvalidDefinitionException.from(InvalidDefinitionException.java:77)
	at com.fasterxml.jackson.databind.SerializerProvider.reportBadDefinition(SerializerProvider.java:1300)
	at com.fasterxml.jackson.databind.ser.impl.UnsupportedTypeSerializer.serialize(UnsupportedTypeSerializer.java:35)
	at com.fasterxml.jackson.databind.ser.BeanPropertyWriter.serializeAsField(BeanPropertyWriter.java:728)
	at com.fasterxml.jackson.databind.ser.std.BeanSerializerBase.serializeFields(BeanSerializerBase.java:774)
	at com.fasterxml.jackson.databind.ser.BeanSerializer.serialize(BeanSerializer.java:178)
	at com.fasterxml.jackson.databind.ser.DefaultSerializerProvider._serialize(DefaultSerializerProvider.java:480)
	at com.fasterxml.jackson.databind.ser.DefaultSerializerProvider.serializeValue(DefaultSerializerProvider.java:319)
	at com.fasterxml.jackson.databind.ObjectMapper.valueToTree(ObjectMapper.java:3389)
	... 17 common frames omitted

2025-09-04 15:50:18.518 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 释放初始化锁
2025-09-04 15:50:18.532 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mc.a.n.client.config.impl.ClientWorker   [0;39m | [fixed-10.180.72.6_8848-dev] [subscribe] kepler-integration+DEFAULT_GROUP+dev
2025-09-04 15:50:18.533 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mc.a.nacos.client.config.impl.CacheData  [0;39m | [fixed-10.180.72.6_8848-dev] [add-listener] ok, tenant=dev, dataId=kepler-integration, group=DEFAULT_GROUP, cnt=1
2025-09-04 15:50:18.534 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mc.a.n.client.config.impl.ClientWorker   [0;39m | [fixed-10.180.72.6_8848-dev] [subscribe] kepler-integration.yaml+DEFAULT_GROUP+dev
2025-09-04 15:50:18.534 | [34m INFO 71225[0;39m | [1;33mmain[0;39m [1;32mc.a.nacos.client.config.impl.CacheData  [0;39m | [fixed-10.180.72.6_8848-dev] [add-listener] ok, tenant=dev, dataId=kepler-integration.yaml, group=DEFAULT_GROUP, cnt=1
2025-09-04 15:50:19.214 | [34m INFO 71225[0;39m | [1;33mcom.alibaba.nacos.client.naming.updater[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | new ips(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-09-04 15:50:19.214 | [34m INFO 71225[0;39m | [1;33mcom.alibaba.nacos.client.naming.updater[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | current ips:(2) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000},{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-09-04 15:50:40.193 | [31m WARN 71225[0;39m | [1;33mThread-16[0;39m [1;32mc.a.nacos.common.notify.NotifyCenter    [0;39m | [NotifyCenter] Start destroying Publisher
2025-09-04 15:50:40.194 | [31m WARN 71225[0;39m | [1;33mThread-16[0;39m [1;32mc.a.nacos.common.notify.NotifyCenter    [0;39m | [NotifyCenter] Destruction of the end
2025-09-04 15:50:40.193 | [31m WARN 71225[0;39m | [1;33mThread-2[0;39m [1;32mc.a.n.common.http.HttpClientBeanHolder  [0;39m | [HttpClientBeanHolder] Start destroying common HttpClient
2025-09-04 15:50:40.196 | [31m WARN 71225[0;39m | [1;33mThread-2[0;39m [1;32mc.a.n.common.http.HttpClientBeanHolder  [0;39m | [HttpClientBeanHolder] Destruction of the end
2025-09-04 15:50:40.222 | [34m INFO 71225[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.a.c.n.registry.NacosServiceRegistry   [0;39m | De-registering from Nacos Server now...
2025-09-04 15:50:40.222 | [34m INFO 71225[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [BEAT] removing beat: DEFAULT_GROUP@@kepler-integration:************:80 from beat map.
2025-09-04 15:50:40.222 | [34m INFO 71225[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [DEREGISTER-SERVICE] dev deregistering service DEFAULT_GROUP@@kepler-integration with instance: Instance{instanceId='null', ip='************', port=80, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={}}
2025-09-04 15:50:40.230 | [34m INFO 71225[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.a.c.n.registry.NacosServiceRegistry   [0;39m | De-registration finished.
2025-09-04 15:50:40.230 | [34m INFO 71225[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown begin
2025-09-04 15:50:43.241 | [34m INFO 71225[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown stop
2025-09-04 15:50:43.242 | [34m INFO 71225[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.core.HostReactor do shutdown begin
2025-09-04 15:50:46.249 | [34m INFO 71225[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.core.PushReceiver do shutdown begin
2025-09-04 15:57:47.331 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mptablePropertiesBeanFactoryPostProcessor[0;39m | Post-processing PropertySource instances
2025-09-04 15:57:47.397 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2025-09-04 15:57:47.399 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 15:57:47.399 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 15:57:47.399 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 15:57:47.399 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-09-04 15:57:47.400 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-09-04 15:57:47.400 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 15:57:47.400 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 15:57:47.455 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mc.u.j.filter.DefaultLazyPropertyFilter  [0;39m | Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-09-04 15:57:47.461 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mc.u.j.r.DefaultLazyPropertyResolver     [0;39m | Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-09-04 15:57:47.462 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mc.u.j.d.DefaultLazyPropertyDetector     [0;39m | Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-09-04 15:57:48.000 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mc.a.n.c.c.impl.LocalConfigInfoProcessor [0;39m | LOCAL_SNAPSHOT_PATH:/Users/<USER>/nacos/config
2025-09-04 15:57:48.027 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mc.a.nacos.client.config.utils.JvmUtil   [0;39m | isMultiInstance:false
2025-09-04 15:57:48.056 | [31m WARN 72879[0;39m | [1;33mmain[0;39m [1;32mc.a.c.n.c.NacosPropertySourceBuilder    [0;39m | Ignore the empty nacos configuration and get it based on dataId[kepler-integration] & group[DEFAULT_GROUP]
2025-09-04 15:57:48.071 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mb.c.PropertySourceBootstrapConfiguration[0;39m | Located property source: [BootstrapPropertySource {name='bootstrapProperties-kepler-integration.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kepler-integration,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-application.yml,DEFAULT_GROUP'}]
2025-09-04 15:57:48.096 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mEnableEncryptablePropertiesConfiguration[0;39m | Bootstraping jasypt-string-boot auto configuration in context: kepler-integration-1
2025-09-04 15:57:48.097 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mc.t.i.KeplerIntegrationServerApplication[0;39m | The following 2 profiles are active: "dev", "uat"
2025-09-04 15:57:49.415 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32m.s.d.r.c.RepositoryConfigurationDelegate[0;39m | Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-04 15:57:49.418 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32m.s.d.r.c.RepositoryConfigurationDelegate[0;39m | Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-04 15:57:49.487 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32m.s.d.r.c.RepositoryConfigurationDelegate[0;39m | Finished Spring Data repository scanning in 41 ms. Found 0 Redis repository interfaces.
2025-09-04 15:57:49.763 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mo.s.cloud.context.scope.GenericScope    [0;39m | BeanFactory id=f8c5ab0f-68e8-3840-9720-c2a4515bce20
2025-09-04 15:57:49.817 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mptablePropertiesBeanFactoryPostProcessor[0;39m | Post-processing PropertySource instances
2025-09-04 15:57:49.830 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrapProperties-kepler-integration.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-09-04 15:57:49.830 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrapProperties-kepler-integration,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-09-04 15:57:49.831 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrapProperties-application.yml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-09-04 15:57:49.831 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2025-09-04 15:57:49.831 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-09-04 15:57:49.831 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-09-04 15:57:49.831 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 15:57:49.831 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 15:57:49.831 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-09-04 15:57:49.831 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-09-04 15:57:49.831 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 15:57:49.831 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 15:57:49.842 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mc.u.j.filter.DefaultLazyPropertyFilter  [0;39m | Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-09-04 15:57:49.849 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mc.u.j.r.DefaultLazyPropertyResolver     [0;39m | Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-09-04 15:57:49.849 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mc.u.j.d.DefaultLazyPropertyDetector     [0;39m | Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-09-04 15:57:50.023 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-04 15:57:50.025 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-04 15:57:50.026 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$662/0x00000008005a7440] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-04 15:57:50.027 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-04 15:57:50.474 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m | Tomcat initialized with port(s): 80 (http)
2025-09-04 15:57:50.485 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mo.apache.catalina.core.StandardService  [0;39m | Starting service [Tomcat]
2025-09-04 15:57:50.486 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32morg.apache.catalina.core.StandardEngine [0;39m | Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-09-04 15:57:50.653 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mo.a.c.c.C.[.[.[/kepler/integration]     [0;39m | Initializing Spring embedded WebApplicationContext
2025-09-04 15:57:50.654 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mw.s.c.ServletWebServerApplicationContext[0;39m | Root WebApplicationContext: initialization completed in 2534 ms
2025-09-04 15:57:50.965 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mf.a.AutowiredAnnotationBeanPostProcessor[0;39m | Autowired annotation is not supported on static fields: private static java.lang.String com.trinasolar.integration.config.security.aes.AESConfig.key
2025-09-04 15:57:51.038 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m | mybatis plus join properties config complete
2025-09-04 15:57:51.511 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1,master} inited
2025-09-04 15:57:51.512 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource - add a datasource named [master] success
2025-09-04 15:57:51.512 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-09-04 15:57:51.578 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m | mybatis plus join SqlInjector init
2025-09-04 15:57:53.858 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-09-04 15:57:53.868 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
2025-09-04 15:57:53.868 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.keyObtentionIterations, using default value: 1000
2025-09-04 15:57:53.868 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.poolSize, using default value: 1
2025-09-04 15:57:53.868 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.providerName, using default value: null
2025-09-04 15:57:53.868 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.providerClassName, using default value: null
2025-09-04 15:57:53.868 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.saltGeneratorClassname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-09-04 15:57:53.869 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.ivGeneratorClassname, using default value: org.jasypt.salt.NoOpIVGenerator
2025-09-04 15:57:53.869 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.stringOutputType, using default value: base64
2025-09-04 15:57:54.141 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32morg.redisson.Version                    [0;39m | Redisson 3.41.0
2025-09-04 15:57:54.221 | [31m WARN 72879[0;39m | [1;33mmain[0;39m [1;32mi.n.r.d.DnsServerAddressStreamProviders [0;39m | Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-09-04 15:57:54.498 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | Redis cluster nodes configuration got from ***********/***********:30012:
461896f00a0c24e3126dc34638854ff6422fb244 ***********:30012@30018 myself,master - 0 1756972673000 1 connected 0-5460
e0b782a8e69537dcc37ea3d2a72258a45ddb2d53 ***********:30014@30020 master - 0 1756972676599 3 connected 10923-16383
93167ed470a2aaf64573d363e6bd6554e03bc4d9 ***********:30013@30019 master - 0 1756972675000 2 connected 5461-10922
0d5fd0961bdeb1f6249808fe9218ccbf5707f630 ***********:30017@30023 slave 461896f00a0c24e3126dc34638854ff6422fb244 0 1756972674593 1 connected
7814d1a5f8f3a16c9b5a1a4212329b85b44e2027 ***********:30015@30021 slave 93167ed470a2aaf64573d363e6bd6554e03bc4d9 0 1756972672586 2 connected
19e5162fb0b4f5bd64d046f5a9a2c2b1db62918e ***********:30016@30022 slave e0b782a8e69537dcc37ea3d2a72258a45ddb2d53 0 1756972675596 3 connected

2025-09-04 15:57:54.542 | [34m INFO 72879[0;39m | [1;33mredisson-netty-1-19[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30012
2025-09-04 15:57:54.542 | [34m INFO 72879[0;39m | [1;33mredisson-netty-1-18[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30014
2025-09-04 15:57:54.563 | [34m INFO 72879[0;39m | [1;33mredisson-netty-1-26[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30013
2025-09-04 15:57:54.863 | [34m INFO 72879[0;39m | [1;33mredisson-netty-1-25[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30014
2025-09-04 15:57:54.871 | [34m INFO 72879[0;39m | [1;33mredisson-netty-1-1[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30012
2025-09-04 15:57:54.887 | [34m INFO 72879[0;39m | [1;33mredisson-netty-1-6[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30013
2025-09-04 15:57:54.903 | [34m INFO 72879[0;39m | [1;33mredisson-netty-1-32[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30016
2025-09-04 15:57:54.903 | [34m INFO 72879[0;39m | [1;33mredisson-netty-1-14[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30017
2025-09-04 15:57:54.920 | [34m INFO 72879[0;39m | [1;33mredisson-netty-1-18[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30015
2025-09-04 15:57:55.213 | [34m INFO 72879[0;39m | [1;33mredisson-netty-1-19[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30016
2025-09-04 15:57:55.214 | [34m INFO 72879[0;39m | [1;33mredisson-netty-1-19[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | slaves: [redis://***********:30016] added for master: redis://***********:30014 slot ranges: [[10923-16383]]
2025-09-04 15:57:55.214 | [34m INFO 72879[0;39m | [1;33mredisson-netty-1-19[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | master: redis://***********:30014 added for slot ranges: [[10923-16383]]
2025-09-04 15:57:55.222 | [34m INFO 72879[0;39m | [1;33mredisson-netty-1-21[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30017
2025-09-04 15:57:55.223 | [34m INFO 72879[0;39m | [1;33mredisson-netty-1-21[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | slaves: [redis://***********:30017] added for master: redis://***********:30012 slot ranges: [[0-5460]]
2025-09-04 15:57:55.223 | [34m INFO 72879[0;39m | [1;33mredisson-netty-1-21[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | master: redis://***********:30012 added for slot ranges: [[0-5460]]
2025-09-04 15:57:55.231 | [34m INFO 72879[0;39m | [1;33mredisson-netty-1-23[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30015
2025-09-04 15:57:55.231 | [34m INFO 72879[0;39m | [1;33mredisson-netty-1-23[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | slaves: [redis://***********:30015] added for master: redis://***********:30013 slot ranges: [[5461-10922]]
2025-09-04 15:57:55.231 | [34m INFO 72879[0;39m | [1;33mredisson-netty-1-23[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | master: redis://***********:30013 added for slot ranges: [[5461-10922]]
2025-09-04 15:57:56.309 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mc.t.t.f.j.c.TsJacksonAutoConfiguration  [0;39m | [init][初始化 JsonUtils 成功]
2025-09-04 15:57:56.759 | [31m WARN 72879[0;39m | [1;33mmain[0;39m [1;32miguration$LoadBalancerCaffeineWarnLogger[0;39m | Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-09-04 15:57:56.935 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | initializer namespace from System Property :null
2025-09-04 15:57:56.936 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | initializer namespace from System Environment :null
2025-09-04 15:57:56.937 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | initializer namespace from System Property :null
2025-09-04 15:57:57.152 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | new ips(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-09-04 15:57:57.162 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | current ips:(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-09-04 15:57:57.185 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m | Tomcat started on port(s): 80 (http) with context path '/kepler/integration'
2025-09-04 15:57:57.190 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [BEAT] adding beat: BeatInfo{port=80, ip='************', weight=1.0, serviceName='DEFAULT_GROUP@@kepler-integration', cluster='DEFAULT', metadata={preserved.heart.beat.timeout=15000, preserved.ip.delete.timeout=30000, preserved.register.source=SPRING_CLOUD, preserved.heart.beat.interval=5000}, scheduled=false, period=5000, stopped=false} to beat map.
2025-09-04 15:57:57.191 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [REGISTER-SERVICE] dev registering service DEFAULT_GROUP@@kepler-integration with instance: Instance{instanceId='null', ip='************', port=80, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.heart.beat.timeout=15000, preserved.ip.delete.timeout=30000, preserved.register.source=SPRING_CLOUD, preserved.heart.beat.interval=5000}}
2025-09-04 15:57:57.199 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mc.a.c.n.registry.NacosServiceRegistry   [0;39m | nacos registry, DEFAULT_GROUP kepler-integration ************:80 register finished
2025-09-04 15:57:57.228 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mc.t.i.KeplerIntegrationServerApplication[0;39m | Started KeplerIntegrationServerApplication in 11.221 seconds (JVM running for 13.014)
2025-09-04 15:57:57.230 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 开始执行数据初始化任务...
2025-09-04 15:57:57.283 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 获取到初始化锁，开始执行数据初始化...
2025-09-04 15:57:57.283 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 开始检查应用系统变更表初始化状态...
2025-09-04 15:57:57.466 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 查询到应用系统数据 73 条，开始全量对比变更表...
2025-09-04 15:57:57.481 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 变更表中已存在版本为1的记录 0 条
2025-09-04 15:57:58.175 | [34m INFO 72879[0;39m | [1;33mcom.alibaba.nacos.client.naming.updater[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | new ips(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-09-04 15:57:58.176 | [34m INFO 72879[0;39m | [1;33mcom.alibaba.nacos.client.naming.updater[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | current ips:(2) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000},{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-09-04 15:57:59.188 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 应用系统变更表初始化完成，跳过 0 条已存在记录，成功插入 73 条新记录
2025-09-04 15:57:59.188 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 开始检查应用程序变更表初始化状态...
2025-09-04 15:57:59.222 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 查询到应用程序数据 426 条，开始全量对比变更表...
2025-09-04 15:57:59.228 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 变更表中已存在版本为0的记录 0 条
2025-09-04 15:58:00.011 | [34m INFO 72879[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.task.ScaScanDistributedTask       [0;39m | =======执行的SCA状态扫描任务======
2025-09-04 15:58:00.264 | [34m INFO 72879[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.task.ScaScanDistributedTask       [0;39m | []
2025-09-04 15:58:06.048 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 应用程序变更表初始化完成，跳过 0 条已存在记录，成功插入 426 条新记录
2025-09-04 15:58:06.048 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 数据初始化任务执行完成
2025-09-04 15:58:06.058 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 释放初始化锁
2025-09-04 15:58:06.073 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mc.a.n.client.config.impl.ClientWorker   [0;39m | [fixed-10.180.72.6_8848-dev] [subscribe] kepler-integration+DEFAULT_GROUP+dev
2025-09-04 15:58:06.074 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mc.a.nacos.client.config.impl.CacheData  [0;39m | [fixed-10.180.72.6_8848-dev] [add-listener] ok, tenant=dev, dataId=kepler-integration, group=DEFAULT_GROUP, cnt=1
2025-09-04 15:58:06.074 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mc.a.n.client.config.impl.ClientWorker   [0;39m | [fixed-10.180.72.6_8848-dev] [subscribe] kepler-integration.yaml+DEFAULT_GROUP+dev
2025-09-04 15:58:06.074 | [34m INFO 72879[0;39m | [1;33mmain[0;39m [1;32mc.a.nacos.client.config.impl.CacheData  [0;39m | [fixed-10.180.72.6_8848-dev] [add-listener] ok, tenant=dev, dataId=kepler-integration.yaml, group=DEFAULT_GROUP, cnt=1
2025-09-04 15:58:57.891 | [31m WARN 72879[0;39m | [1;33mThread-16[0;39m [1;32mc.a.nacos.common.notify.NotifyCenter    [0;39m | [NotifyCenter] Start destroying Publisher
2025-09-04 15:58:57.891 | [31m WARN 72879[0;39m | [1;33mThread-16[0;39m [1;32mc.a.nacos.common.notify.NotifyCenter    [0;39m | [NotifyCenter] Destruction of the end
2025-09-04 15:58:57.892 | [31m WARN 72879[0;39m | [1;33mThread-2[0;39m [1;32mc.a.n.common.http.HttpClientBeanHolder  [0;39m | [HttpClientBeanHolder] Start destroying common HttpClient
2025-09-04 15:58:57.894 | [31m WARN 72879[0;39m | [1;33mThread-2[0;39m [1;32mc.a.n.common.http.HttpClientBeanHolder  [0;39m | [HttpClientBeanHolder] Destruction of the end
2025-09-04 15:58:57.935 | [34m INFO 72879[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.a.c.n.registry.NacosServiceRegistry   [0;39m | De-registering from Nacos Server now...
2025-09-04 15:58:57.936 | [34m INFO 72879[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [BEAT] removing beat: DEFAULT_GROUP@@kepler-integration:************:80 from beat map.
2025-09-04 15:58:57.936 | [34m INFO 72879[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [DEREGISTER-SERVICE] dev deregistering service DEFAULT_GROUP@@kepler-integration with instance: Instance{instanceId='null', ip='************', port=80, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={}}
2025-09-04 15:58:57.946 | [34m INFO 72879[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.a.c.n.registry.NacosServiceRegistry   [0;39m | De-registration finished.
2025-09-04 15:58:57.946 | [34m INFO 72879[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown begin
2025-09-04 15:59:00.957 | [34m INFO 72879[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown stop
2025-09-04 15:59:00.958 | [34m INFO 72879[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.core.HostReactor do shutdown begin
2025-09-04 15:59:03.967 | [34m INFO 72879[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.core.PushReceiver do shutdown begin
2025-09-04 15:59:06.976 | [34m INFO 72879[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.core.PushReceiver do shutdown stop
2025-09-04 15:59:06.976 | [34m INFO 72879[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin
2025-09-04 15:59:06.976 | [34m INFO 72879[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop
2025-09-04 15:59:06.976 | [34m INFO 72879[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.core.HostReactor do shutdown stop
2025-09-04 15:59:06.976 | [34m INFO 72879[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.net.NamingProxy do shutdown begin
2025-09-04 15:59:06.977 | [31m WARN 72879[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [NamingHttpClientManager] Start destroying NacosRestTemplate
2025-09-04 15:59:06.977 | [31m WARN 72879[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [NamingHttpClientManager] Destruction of the end
2025-09-04 15:59:06.977 | [34m INFO 72879[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.a.n.client.identify.CredentialWatcher [0;39m | [null] CredentialWatcher is stopped
2025-09-04 15:59:06.977 | [34m INFO 72879[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.a.n.client.identify.CredentialService [0;39m | [null] CredentialService is freed
2025-09-04 15:59:06.977 | [34m INFO 72879[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.net.NamingProxy do shutdown stop
2025-09-04 15:59:06.977 | [31m WARN 72879[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mo.s.b.f.support.DisposableBeanAdapter   [0;39m | Custom destroy method 'close' on bean with name 'nacosServiceRegistry' threw an exception: java.lang.NullPointerException
2025-09-04 15:59:07.019 | [34m INFO 72879[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource start closing ....
2025-09-04 15:59:07.021 | [34m INFO 72879[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1} closing ...
2025-09-04 15:59:07.028 | [34m INFO 72879[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1} closed
2025-09-04 15:59:07.028 | [34m INFO 72879[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.b.d.d.d.DefaultDataSourceDestroyer    [0;39m | dynamic-datasource close the datasource named [master] success,
2025-09-04 15:59:07.028 | [34m INFO 72879[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource all closed success,bye
2025-09-04 16:36:51.289 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mptablePropertiesBeanFactoryPostProcessor[0;39m | Post-processing PropertySource instances
2025-09-04 16:36:51.368 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2025-09-04 16:36:51.370 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 16:36:51.370 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 16:36:51.370 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 16:36:51.371 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-09-04 16:36:51.371 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-09-04 16:36:51.371 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 16:36:51.371 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 16:36:51.428 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mc.u.j.filter.DefaultLazyPropertyFilter  [0;39m | Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-09-04 16:36:51.436 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mc.u.j.r.DefaultLazyPropertyResolver     [0;39m | Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-09-04 16:36:51.438 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mc.u.j.d.DefaultLazyPropertyDetector     [0;39m | Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-09-04 16:36:51.962 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mc.a.n.c.c.impl.LocalConfigInfoProcessor [0;39m | LOCAL_SNAPSHOT_PATH:/Users/<USER>/nacos/config
2025-09-04 16:36:51.991 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mc.a.nacos.client.config.utils.JvmUtil   [0;39m | isMultiInstance:false
2025-09-04 16:36:52.019 | [31m WARN 81077[0;39m | [1;33mmain[0;39m [1;32mc.a.c.n.c.NacosPropertySourceBuilder    [0;39m | Ignore the empty nacos configuration and get it based on dataId[kepler-integration] & group[DEFAULT_GROUP]
2025-09-04 16:36:52.034 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mb.c.PropertySourceBootstrapConfiguration[0;39m | Located property source: [BootstrapPropertySource {name='bootstrapProperties-kepler-integration.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kepler-integration,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-application.yml,DEFAULT_GROUP'}]
2025-09-04 16:36:52.062 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mEnableEncryptablePropertiesConfiguration[0;39m | Bootstraping jasypt-string-boot auto configuration in context: kepler-integration-1
2025-09-04 16:36:52.063 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mc.t.i.KeplerIntegrationServerApplication[0;39m | The following 2 profiles are active: "dev", "uat"
2025-09-04 16:36:53.886 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32m.s.d.r.c.RepositoryConfigurationDelegate[0;39m | Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-04 16:36:53.889 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32m.s.d.r.c.RepositoryConfigurationDelegate[0;39m | Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-04 16:36:53.968 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32m.s.d.r.c.RepositoryConfigurationDelegate[0;39m | Finished Spring Data repository scanning in 53 ms. Found 0 Redis repository interfaces.
2025-09-04 16:36:54.267 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mo.s.cloud.context.scope.GenericScope    [0;39m | BeanFactory id=f8c5ab0f-68e8-3840-9720-c2a4515bce20
2025-09-04 16:36:54.328 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mptablePropertiesBeanFactoryPostProcessor[0;39m | Post-processing PropertySource instances
2025-09-04 16:36:54.342 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrapProperties-kepler-integration.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-09-04 16:36:54.343 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrapProperties-kepler-integration,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-09-04 16:36:54.343 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrapProperties-application.yml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-09-04 16:36:54.343 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2025-09-04 16:36:54.343 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-09-04 16:36:54.343 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-09-04 16:36:54.343 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 16:36:54.343 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 16:36:54.343 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-09-04 16:36:54.343 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-09-04 16:36:54.344 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 16:36:54.344 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 16:36:54.353 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mc.u.j.filter.DefaultLazyPropertyFilter  [0;39m | Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-09-04 16:36:54.362 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mc.u.j.r.DefaultLazyPropertyResolver     [0;39m | Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-09-04 16:36:54.362 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mc.u.j.d.DefaultLazyPropertyDetector     [0;39m | Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-09-04 16:36:54.559 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-04 16:36:54.562 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-04 16:36:54.564 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$665/0x00000008005a7440] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-04 16:36:54.565 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-04 16:36:55.092 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m | Tomcat initialized with port(s): 80 (http)
2025-09-04 16:36:55.105 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mo.apache.catalina.core.StandardService  [0;39m | Starting service [Tomcat]
2025-09-04 16:36:55.106 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32morg.apache.catalina.core.StandardEngine [0;39m | Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-09-04 16:36:55.285 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mo.a.c.c.C.[.[.[/kepler/integration]     [0;39m | Initializing Spring embedded WebApplicationContext
2025-09-04 16:36:55.286 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mw.s.c.ServletWebServerApplicationContext[0;39m | Root WebApplicationContext: initialization completed in 3196 ms
2025-09-04 16:36:55.790 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mf.a.AutowiredAnnotationBeanPostProcessor[0;39m | Autowired annotation is not supported on static fields: private static java.lang.String com.trinasolar.integration.config.security.aes.AESConfig.key
2025-09-04 16:36:55.868 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m | mybatis plus join properties config complete
2025-09-04 16:36:56.324 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1,master} inited
2025-09-04 16:36:56.326 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource - add a datasource named [master] success
2025-09-04 16:36:56.326 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-09-04 16:36:56.393 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m | mybatis plus join SqlInjector init
2025-09-04 16:36:58.776 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-09-04 16:36:58.785 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
2025-09-04 16:36:58.785 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.keyObtentionIterations, using default value: 1000
2025-09-04 16:36:58.785 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.poolSize, using default value: 1
2025-09-04 16:36:58.785 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.providerName, using default value: null
2025-09-04 16:36:58.785 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.providerClassName, using default value: null
2025-09-04 16:36:58.785 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.saltGeneratorClassname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-09-04 16:36:58.786 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.ivGeneratorClassname, using default value: org.jasypt.salt.NoOpIVGenerator
2025-09-04 16:36:58.787 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.stringOutputType, using default value: base64
2025-09-04 16:36:59.071 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32morg.redisson.Version                    [0;39m | Redisson 3.41.0
2025-09-04 16:36:59.155 | [31m WARN 81077[0;39m | [1;33mmain[0;39m [1;32mi.n.r.d.DnsServerAddressStreamProviders [0;39m | Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-09-04 16:36:59.439 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | Redis cluster nodes configuration got from ***********/***********:30012:
461896f00a0c24e3126dc34638854ff6422fb244 ***********:30012@30018 myself,master - 0 1756975018000 1 connected 0-5460
e0b782a8e69537dcc37ea3d2a72258a45ddb2d53 ***********:30014@30020 master - 0 1756975019000 3 connected 10923-16383
93167ed470a2aaf64573d363e6bd6554e03bc4d9 ***********:30013@30019 master - 0 1756975019892 2 connected 5461-10922
0d5fd0961bdeb1f6249808fe9218ccbf5707f630 ***********:30017@30023 slave 461896f00a0c24e3126dc34638854ff6422fb244 0 1756975020896 1 connected
7814d1a5f8f3a16c9b5a1a4212329b85b44e2027 ***********:30015@30021 slave 93167ed470a2aaf64573d363e6bd6554e03bc4d9 0 1756975019000 2 connected
19e5162fb0b4f5bd64d046f5a9a2c2b1db62918e ***********:30016@30022 slave e0b782a8e69537dcc37ea3d2a72258a45ddb2d53 0 1756975018000 3 connected

2025-09-04 16:36:59.484 | [34m INFO 81077[0;39m | [1;33mredisson-netty-1-19[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30012
2025-09-04 16:36:59.483 | [34m INFO 81077[0;39m | [1;33mredisson-netty-1-18[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30014
2025-09-04 16:36:59.500 | [34m INFO 81077[0;39m | [1;33mredisson-netty-1-26[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30013
2025-09-04 16:36:59.781 | [34m INFO 81077[0;39m | [1;33mredisson-netty-1-24[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30012
2025-09-04 16:36:59.787 | [34m INFO 81077[0;39m | [1;33mredisson-netty-1-26[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30014
2025-09-04 16:36:59.800 | [34m INFO 81077[0;39m | [1;33mredisson-netty-1-9[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30013
2025-09-04 16:36:59.809 | [34m INFO 81077[0;39m | [1;33mredisson-netty-1-12[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30017
2025-09-04 16:36:59.809 | [34m INFO 81077[0;39m | [1;33mredisson-netty-1-13[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30016
2025-09-04 16:36:59.824 | [34m INFO 81077[0;39m | [1;33mredisson-netty-1-21[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30015
2025-09-04 16:37:00.105 | [34m INFO 81077[0;39m | [1;33mredisson-netty-1-32[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30016
2025-09-04 16:37:00.106 | [34m INFO 81077[0;39m | [1;33mredisson-netty-1-32[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | slaves: [redis://***********:30016] added for master: redis://***********:30014 slot ranges: [[10923-16383]]
2025-09-04 16:37:00.107 | [34m INFO 81077[0;39m | [1;33mredisson-netty-1-32[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | master: redis://***********:30014 added for slot ranges: [[10923-16383]]
2025-09-04 16:37:00.124 | [34m INFO 81077[0;39m | [1;33mredisson-netty-1-21[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30017
2025-09-04 16:37:00.124 | [34m INFO 81077[0;39m | [1;33mredisson-netty-1-21[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | slaves: [redis://***********:30017] added for master: redis://***********:30012 slot ranges: [[0-5460]]
2025-09-04 16:37:00.124 | [34m INFO 81077[0;39m | [1;33mredisson-netty-1-21[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | master: redis://***********:30012 added for slot ranges: [[0-5460]]
2025-09-04 16:37:00.129 | [34m INFO 81077[0;39m | [1;33mredisson-netty-1-22[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30015
2025-09-04 16:37:00.130 | [34m INFO 81077[0;39m | [1;33mredisson-netty-1-22[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | slaves: [redis://***********:30015] added for master: redis://***********:30013 slot ranges: [[5461-10922]]
2025-09-04 16:37:00.130 | [34m INFO 81077[0;39m | [1;33mredisson-netty-1-22[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | master: redis://***********:30013 added for slot ranges: [[5461-10922]]
2025-09-04 16:37:01.369 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mc.t.t.f.j.c.TsJacksonAutoConfiguration  [0;39m | [init][初始化 JsonUtils 成功]
2025-09-04 16:37:01.832 | [31m WARN 81077[0;39m | [1;33mmain[0;39m [1;32miguration$LoadBalancerCaffeineWarnLogger[0;39m | Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-09-04 16:37:02.094 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | initializer namespace from System Property :null
2025-09-04 16:37:02.095 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | initializer namespace from System Environment :null
2025-09-04 16:37:02.095 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | initializer namespace from System Property :null
2025-09-04 16:37:02.355 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | new ips(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"***********#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"***********","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-09-04 16:37:02.363 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | current ips:(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"***********#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"***********","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-09-04 16:37:02.379 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m | Tomcat started on port(s): 80 (http) with context path '/kepler/integration'
2025-09-04 16:37:02.383 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [BEAT] adding beat: BeatInfo{port=80, ip='************', weight=1.0, serviceName='DEFAULT_GROUP@@kepler-integration', cluster='DEFAULT', metadata={preserved.heart.beat.timeout=15000, preserved.ip.delete.timeout=30000, preserved.register.source=SPRING_CLOUD, preserved.heart.beat.interval=5000}, scheduled=false, period=5000, stopped=false} to beat map.
2025-09-04 16:37:02.384 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [REGISTER-SERVICE] dev registering service DEFAULT_GROUP@@kepler-integration with instance: Instance{instanceId='null', ip='************', port=80, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.heart.beat.timeout=15000, preserved.ip.delete.timeout=30000, preserved.register.source=SPRING_CLOUD, preserved.heart.beat.interval=5000}}
2025-09-04 16:37:02.392 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mc.a.c.n.registry.NacosServiceRegistry   [0;39m | nacos registry, DEFAULT_GROUP kepler-integration ************:80 register finished
2025-09-04 16:37:02.416 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mc.t.i.KeplerIntegrationServerApplication[0;39m | Started KeplerIntegrationServerApplication in 12.791 seconds (JVM running for 14.128)
2025-09-04 16:37:02.418 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 开始执行数据初始化任务...
2025-09-04 16:37:02.454 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 获取到初始化锁，开始执行数据初始化...
2025-09-04 16:37:02.454 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 开始检查应用系统变更表初始化状态...
2025-09-04 16:37:02.630 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 查询到应用系统数据 73 条，开始全量对比变更表...
2025-09-04 16:37:02.664 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 变更表中已存在版本为1的记录 73 条
2025-09-04 16:37:02.664 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 应用系统变更表初始化完成，跳过 73 条已存在记录，成功插入 0 条新记录
2025-09-04 16:37:02.664 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 开始检查应用程序变更表初始化状态...
2025-09-04 16:37:02.692 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 查询到应用程序数据 426 条，开始全量对比变更表...
2025-09-04 16:37:02.736 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 变更表中已存在版本为0的记录 426 条
2025-09-04 16:37:02.737 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 应用程序变更表初始化完成，跳过 426 条已存在记录，成功插入 0 条新记录
2025-09-04 16:37:02.737 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 数据初始化任务执行完成
2025-09-04 16:37:02.756 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 释放初始化锁
2025-09-04 16:37:02.770 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mc.a.n.client.config.impl.ClientWorker   [0;39m | [fixed-10.180.72.6_8848-dev] [subscribe] kepler-integration+DEFAULT_GROUP+dev
2025-09-04 16:37:02.772 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mc.a.nacos.client.config.impl.CacheData  [0;39m | [fixed-10.180.72.6_8848-dev] [add-listener] ok, tenant=dev, dataId=kepler-integration, group=DEFAULT_GROUP, cnt=1
2025-09-04 16:37:02.772 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mc.a.n.client.config.impl.ClientWorker   [0;39m | [fixed-10.180.72.6_8848-dev] [subscribe] kepler-integration.yaml+DEFAULT_GROUP+dev
2025-09-04 16:37:02.772 | [34m INFO 81077[0;39m | [1;33mmain[0;39m [1;32mc.a.nacos.client.config.impl.CacheData  [0;39m | [fixed-10.180.72.6_8848-dev] [add-listener] ok, tenant=dev, dataId=kepler-integration.yaml, group=DEFAULT_GROUP, cnt=1
2025-09-04 16:37:02.788 | [34m INFO 81077[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mc.a.n.client.config.impl.ClientWorker   [0;39m | [fixed-10.180.72.6_8848-dev] [polling-resp] config changed. dataId=kepler-integration.yaml, group=DEFAULT_GROUP, tenant=dev
2025-09-04 16:37:02.789 | [34m INFO 81077[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mc.a.n.client.config.impl.ClientWorker   [0;39m | get changedGroupKeys:[kepler-integration.yaml+DEFAULT_GROUP+dev]
2025-09-04 16:37:02.798 | [34m INFO 81077[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mc.a.n.client.config.impl.ClientWorker   [0;39m | [fixed-10.180.72.6_8848-dev] [data-received] dataId=kepler-integration.yaml, group=DEFAULT_GROUP, tenant=dev, md5=c88d18a6372eb501d64deb62ba10468a, content=server:
  port: 80
  servlet:
    context-path: /kepler/integration
spring:
  application:
    name:..., type=yaml
2025-09-04 16:37:02.800 | [34m INFO 81077[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mc.a.nacos.client.config.impl.CacheData  [0;39m | [fixed-10.180.72.6_8848-dev] [notify-context] dataId=kepler-integration.yaml, group=DEFAULT_GROUP, md5=c88d18a6372eb501d64deb62ba10468a
2025-09-04 16:37:02.879 | [34m INFO 81077[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mptablePropertiesBeanFactoryPostProcessor[0;39m | Post-processing PropertySource instances
2025-09-04 16:37:02.884 | [34m INFO 81077[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2025-09-04 16:37:02.885 | [34m INFO 81077[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 16:37:02.885 | [34m INFO 81077[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource refreshArgs [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 16:37:02.885 | [34m INFO 81077[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 16:37:02.885 | [34m INFO 81077[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 16:37:02.885 | [34m INFO 81077[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-09-04 16:37:02.885 | [34m INFO 81077[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-09-04 16:37:02.885 | [34m INFO 81077[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 16:37:02.885 | [34m INFO 81077[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 16:37:02.889 | [34m INFO 81077[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mc.u.j.filter.DefaultLazyPropertyFilter  [0;39m | Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-09-04 16:37:02.889 | [34m INFO 81077[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mc.u.j.r.DefaultLazyPropertyResolver     [0;39m | Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-09-04 16:37:02.889 | [34m INFO 81077[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mc.u.j.d.DefaultLazyPropertyDetector     [0;39m | Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-09-04 16:37:02.913 | [31m WARN 81077[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mc.a.c.n.c.NacosPropertySourceBuilder    [0;39m | Ignore the empty nacos configuration and get it based on dataId[kepler-integration] & group[DEFAULT_GROUP]
2025-09-04 16:37:02.933 | [31m WARN 81077[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mc.a.c.n.c.NacosPropertySourceBuilder    [0;39m | Ignore the empty nacos configuration and get it based on dataId[kepler-integration-dev.yaml] & group[DEFAULT_GROUP]
2025-09-04 16:37:02.940 | [31m WARN 81077[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mc.a.c.n.c.NacosPropertySourceBuilder    [0;39m | Ignore the empty nacos configuration and get it based on dataId[kepler-integration-uat.yaml] & group[DEFAULT_GROUP]
2025-09-04 16:37:02.940 | [34m INFO 81077[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mb.c.PropertySourceBootstrapConfiguration[0;39m | Located property source: [BootstrapPropertySource {name='bootstrapProperties-kepler-integration-uat.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kepler-integration-dev.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kepler-integration.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kepler-integration,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-application.yml,DEFAULT_GROUP'}]
2025-09-04 16:37:02.963 | [34m INFO 81077[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mEnableEncryptablePropertiesConfiguration[0;39m | Bootstraping jasypt-string-boot auto configuration in context: kepler-integration-1
2025-09-04 16:37:02.963 | [34m INFO 81077[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mo.s.boot.SpringApplication              [0;39m | The following 2 profiles are active: "dev", "uat"
2025-09-04 16:37:02.974 | [34m INFO 81077[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mo.s.boot.SpringApplication              [0;39m | Started application in 0.165 seconds (JVM running for 14.686)
2025-09-04 16:37:03.243 | [34m INFO 81077[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mu.j.c.RefreshScopeRefreshedEventListener[0;39m | Refreshing cached encryptable property sources
2025-09-04 16:37:03.244 | [34m INFO 81077[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mu.j.c.RefreshScopeRefreshedEventListener[0;39m | Refreshing cached encryptable property sources
2025-09-04 16:37:03.244 | [34m INFO 81077[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mo.s.c.e.event.RefreshEventListener      [0;39m | Refresh keys changed: [spring.redis.password, spring.datasource.username, oss.secret-key, schedule.datashare.system-full-cron, oss.access-key, spring.smart-redisson.password, spring.datasource.password]
2025-09-04 16:37:03.245 | [34m INFO 81077[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mc.a.nacos.client.config.impl.CacheData  [0;39m | [fixed-10.180.72.6_8848-dev] [notify-ok] dataId=kepler-integration.yaml, group=DEFAULT_GROUP, md5=c88d18a6372eb501d64deb62ba10468a, listener=com.alibaba.cloud.nacos.refresh.NacosContextRefresher$1@369f8bdc 
2025-09-04 16:37:03.245 | [34m INFO 81077[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mc.a.nacos.client.config.impl.CacheData  [0;39m | [fixed-10.180.72.6_8848-dev] [notify-listener] time cost=445ms in ClientWorker, dataId=kepler-integration.yaml, group=DEFAULT_GROUP, md5=c88d18a6372eb501d64deb62ba10468a, listener=com.alibaba.cloud.nacos.refresh.NacosContextRefresher$1@369f8bdc 
2025-09-04 16:37:03.379 | [34m INFO 81077[0;39m | [1;33mcom.alibaba.nacos.client.naming.updater[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | new ips(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-09-04 16:37:03.381 | [34m INFO 81077[0;39m | [1;33mcom.alibaba.nacos.client.naming.updater[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | current ips:(2) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000},{"instanceId":"***********#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"***********","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-09-04 16:37:08.970 | [31m WARN 81077[0;39m | [1;33mThread-16[0;39m [1;32mc.a.nacos.common.notify.NotifyCenter    [0;39m | [NotifyCenter] Start destroying Publisher
2025-09-04 16:37:08.970 | [31m WARN 81077[0;39m | [1;33mThread-16[0;39m [1;32mc.a.nacos.common.notify.NotifyCenter    [0;39m | [NotifyCenter] Destruction of the end
2025-09-04 16:37:08.970 | [31m WARN 81077[0;39m | [1;33mThread-2[0;39m [1;32mc.a.n.common.http.HttpClientBeanHolder  [0;39m | [HttpClientBeanHolder] Start destroying common HttpClient
2025-09-04 16:37:08.971 | [31m WARN 81077[0;39m | [1;33mThread-2[0;39m [1;32mc.a.n.common.http.HttpClientBeanHolder  [0;39m | [HttpClientBeanHolder] Destruction of the end
2025-09-04 16:37:09.004 | [34m INFO 81077[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.a.c.n.registry.NacosServiceRegistry   [0;39m | De-registering from Nacos Server now...
2025-09-04 16:37:09.004 | [34m INFO 81077[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [BEAT] removing beat: DEFAULT_GROUP@@kepler-integration:************:80 from beat map.
2025-09-04 16:37:09.005 | [34m INFO 81077[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [DEREGISTER-SERVICE] dev deregistering service DEFAULT_GROUP@@kepler-integration with instance: Instance{instanceId='null', ip='************', port=80, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={}}
2025-09-04 16:37:09.013 | [34m INFO 81077[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.a.c.n.registry.NacosServiceRegistry   [0;39m | De-registration finished.
2025-09-04 16:37:09.013 | [34m INFO 81077[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown begin
2025-09-04 16:37:12.022 | [34m INFO 81077[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown stop
2025-09-04 16:37:12.022 | [34m INFO 81077[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.core.HostReactor do shutdown begin
2025-09-04 16:37:13.394 | [34m INFO 81077[0;39m | [1;33mcom.alibaba.nacos.client.naming.updater[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | removed ips(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-09-04 16:37:13.395 | [34m INFO 81077[0;39m | [1;33mcom.alibaba.nacos.client.naming.updater[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | current ips:(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"***********#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"***********","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-09-04 16:37:13.396 | [34m INFO 81077[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.core.PushReceiver do shutdown begin
2025-09-04 16:37:16.406 | [34m INFO 81077[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.core.PushReceiver do shutdown stop
2025-09-04 16:37:16.406 | [34m INFO 81077[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin
2025-09-04 16:37:16.407 | [34m INFO 81077[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop
2025-09-04 16:37:16.407 | [34m INFO 81077[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.core.HostReactor do shutdown stop
2025-09-04 16:37:16.407 | [34m INFO 81077[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.net.NamingProxy do shutdown begin
2025-09-04 16:37:16.407 | [31m WARN 81077[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [NamingHttpClientManager] Start destroying NacosRestTemplate
2025-09-04 16:37:16.408 | [31m WARN 81077[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [NamingHttpClientManager] Destruction of the end
2025-09-04 16:37:16.408 | [34m INFO 81077[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.a.n.client.identify.CredentialWatcher [0;39m | [null] CredentialWatcher is stopped
2025-09-04 16:37:16.408 | [34m INFO 81077[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.a.n.client.identify.CredentialService [0;39m | [null] CredentialService is freed
2025-09-04 16:37:16.408 | [34m INFO 81077[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.net.NamingProxy do shutdown stop
2025-09-04 16:37:16.409 | [31m WARN 81077[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mo.s.b.f.support.DisposableBeanAdapter   [0;39m | Custom destroy method 'close' on bean with name 'nacosServiceRegistry' threw an exception: java.lang.NullPointerException
2025-09-04 16:37:16.458 | [34m INFO 81077[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource start closing ....
2025-09-04 16:37:16.459 | [34m INFO 81077[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1} closing ...
2025-09-04 16:37:16.466 | [34m INFO 81077[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1} closed
2025-09-04 16:37:16.466 | [34m INFO 81077[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.b.d.d.d.DefaultDataSourceDestroyer    [0;39m | dynamic-datasource close the datasource named [master] success,
2025-09-04 16:37:16.466 | [34m INFO 81077[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource all closed success,bye
2025-09-04 16:37:19.746 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mptablePropertiesBeanFactoryPostProcessor[0;39m | Post-processing PropertySource instances
2025-09-04 16:37:19.806 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2025-09-04 16:37:19.807 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 16:37:19.807 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 16:37:19.808 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 16:37:19.808 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-09-04 16:37:19.808 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-09-04 16:37:19.808 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 16:37:19.808 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 16:37:19.853 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mc.u.j.filter.DefaultLazyPropertyFilter  [0;39m | Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-09-04 16:37:19.857 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mc.u.j.r.DefaultLazyPropertyResolver     [0;39m | Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-09-04 16:37:19.858 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mc.u.j.d.DefaultLazyPropertyDetector     [0;39m | Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-09-04 16:37:20.299 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mc.a.n.c.c.impl.LocalConfigInfoProcessor [0;39m | LOCAL_SNAPSHOT_PATH:/Users/<USER>/nacos/config
2025-09-04 16:37:20.332 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mc.a.nacos.client.config.utils.JvmUtil   [0;39m | isMultiInstance:false
2025-09-04 16:37:20.360 | [31m WARN 81206[0;39m | [1;33mmain[0;39m [1;32mc.a.c.n.c.NacosPropertySourceBuilder    [0;39m | Ignore the empty nacos configuration and get it based on dataId[kepler-integration] & group[DEFAULT_GROUP]
2025-09-04 16:37:20.375 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mb.c.PropertySourceBootstrapConfiguration[0;39m | Located property source: [BootstrapPropertySource {name='bootstrapProperties-kepler-integration.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kepler-integration,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-application.yml,DEFAULT_GROUP'}]
2025-09-04 16:37:20.406 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mEnableEncryptablePropertiesConfiguration[0;39m | Bootstraping jasypt-string-boot auto configuration in context: kepler-integration-1
2025-09-04 16:37:20.407 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mc.t.i.KeplerIntegrationServerApplication[0;39m | The following 2 profiles are active: "dev", "uat"
2025-09-04 16:37:21.593 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32m.s.d.r.c.RepositoryConfigurationDelegate[0;39m | Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-04 16:37:21.596 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32m.s.d.r.c.RepositoryConfigurationDelegate[0;39m | Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-04 16:37:21.657 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32m.s.d.r.c.RepositoryConfigurationDelegate[0;39m | Finished Spring Data repository scanning in 37 ms. Found 0 Redis repository interfaces.
2025-09-04 16:37:21.923 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mo.s.cloud.context.scope.GenericScope    [0;39m | BeanFactory id=f8c5ab0f-68e8-3840-9720-c2a4515bce20
2025-09-04 16:37:21.979 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mptablePropertiesBeanFactoryPostProcessor[0;39m | Post-processing PropertySource instances
2025-09-04 16:37:21.991 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrapProperties-kepler-integration.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-09-04 16:37:21.992 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrapProperties-kepler-integration,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-09-04 16:37:21.992 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrapProperties-application.yml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-09-04 16:37:21.992 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2025-09-04 16:37:21.992 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-09-04 16:37:21.992 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-09-04 16:37:21.992 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 16:37:21.992 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 16:37:21.992 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-09-04 16:37:21.992 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-09-04 16:37:21.992 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 16:37:21.992 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 16:37:22.003 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mc.u.j.filter.DefaultLazyPropertyFilter  [0;39m | Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-09-04 16:37:22.010 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mc.u.j.r.DefaultLazyPropertyResolver     [0;39m | Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-09-04 16:37:22.011 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mc.u.j.d.DefaultLazyPropertyDetector     [0;39m | Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-09-04 16:37:22.166 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-04 16:37:22.168 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-04 16:37:22.170 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$662/0x00000008005a6440] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-04 16:37:22.171 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-04 16:37:22.625 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m | Tomcat initialized with port(s): 80 (http)
2025-09-04 16:37:22.636 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mo.apache.catalina.core.StandardService  [0;39m | Starting service [Tomcat]
2025-09-04 16:37:22.636 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32morg.apache.catalina.core.StandardEngine [0;39m | Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-09-04 16:37:22.777 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mo.a.c.c.C.[.[.[/kepler/integration]     [0;39m | Initializing Spring embedded WebApplicationContext
2025-09-04 16:37:22.777 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mw.s.c.ServletWebServerApplicationContext[0;39m | Root WebApplicationContext: initialization completed in 2341 ms
2025-09-04 16:37:23.084 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mf.a.AutowiredAnnotationBeanPostProcessor[0;39m | Autowired annotation is not supported on static fields: private static java.lang.String com.trinasolar.integration.config.security.aes.AESConfig.key
2025-09-04 16:37:23.144 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m | mybatis plus join properties config complete
2025-09-04 16:37:23.566 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1,master} inited
2025-09-04 16:37:23.567 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource - add a datasource named [master] success
2025-09-04 16:37:23.568 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-09-04 16:37:23.626 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m | mybatis plus join SqlInjector init
2025-09-04 16:37:25.840 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-09-04 16:37:25.849 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
2025-09-04 16:37:25.849 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.keyObtentionIterations, using default value: 1000
2025-09-04 16:37:25.849 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.poolSize, using default value: 1
2025-09-04 16:37:25.850 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.providerName, using default value: null
2025-09-04 16:37:25.850 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.providerClassName, using default value: null
2025-09-04 16:37:25.850 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.saltGeneratorClassname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-09-04 16:37:25.850 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.ivGeneratorClassname, using default value: org.jasypt.salt.NoOpIVGenerator
2025-09-04 16:37:25.851 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.stringOutputType, using default value: base64
2025-09-04 16:37:26.098 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32morg.redisson.Version                    [0;39m | Redisson 3.41.0
2025-09-04 16:37:26.176 | [31m WARN 81206[0;39m | [1;33mmain[0;39m [1;32mi.n.r.d.DnsServerAddressStreamProviders [0;39m | Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-09-04 16:37:26.451 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | Redis cluster nodes configuration got from ***********/***********:30012:
461896f00a0c24e3126dc34638854ff6422fb244 ***********:30012@30018 myself,master - 0 1756975043000 1 connected 0-5460
e0b782a8e69537dcc37ea3d2a72258a45ddb2d53 ***********:30014@30020 master - 0 1756975048003 3 connected 10923-16383
93167ed470a2aaf64573d363e6bd6554e03bc4d9 ***********:30013@30019 master - 0 1756975046998 2 connected 5461-10922
0d5fd0961bdeb1f6249808fe9218ccbf5707f630 ***********:30017@30023 slave 461896f00a0c24e3126dc34638854ff6422fb244 0 1756975045994 1 connected
7814d1a5f8f3a16c9b5a1a4212329b85b44e2027 ***********:30015@30021 slave 93167ed470a2aaf64573d363e6bd6554e03bc4d9 0 1756975045000 2 connected
19e5162fb0b4f5bd64d046f5a9a2c2b1db62918e ***********:30016@30022 slave e0b782a8e69537dcc37ea3d2a72258a45ddb2d53 0 1756975046000 3 connected

2025-09-04 16:37:26.513 | [34m INFO 81206[0;39m | [1;33mredisson-netty-1-18[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30012
2025-09-04 16:37:26.512 | [34m INFO 81206[0;39m | [1;33mredisson-netty-1-17[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30014
2025-09-04 16:37:26.540 | [34m INFO 81206[0;39m | [1;33mredisson-netty-1-26[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30013
2025-09-04 16:37:26.868 | [34m INFO 81206[0;39m | [1;33mredisson-netty-1-23[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30012
2025-09-04 16:37:26.891 | [34m INFO 81206[0;39m | [1;33mredisson-netty-1-28[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30014
2025-09-04 16:37:26.899 | [34m INFO 81206[0;39m | [1;33mredisson-netty-1-6[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30013
2025-09-04 16:37:26.910 | [34m INFO 81206[0;39m | [1;33mredisson-netty-1-12[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30017
2025-09-04 16:37:26.916 | [34m INFO 81206[0;39m | [1;33mredisson-netty-1-32[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30016
2025-09-04 16:37:26.930 | [34m INFO 81206[0;39m | [1;33mredisson-netty-1-21[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30015
2025-09-04 16:37:27.214 | [34m INFO 81206[0;39m | [1;33mredisson-netty-1-16[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30017
2025-09-04 16:37:27.215 | [34m INFO 81206[0;39m | [1;33mredisson-netty-1-16[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | slaves: [redis://***********:30017] added for master: redis://***********:30012 slot ranges: [[0-5460]]
2025-09-04 16:37:27.215 | [34m INFO 81206[0;39m | [1;33mredisson-netty-1-16[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | master: redis://***********:30012 added for slot ranges: [[0-5460]]
2025-09-04 16:37:27.220 | [34m INFO 81206[0;39m | [1;33mredisson-netty-1-19[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30016
2025-09-04 16:37:27.221 | [34m INFO 81206[0;39m | [1;33mredisson-netty-1-19[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | slaves: [redis://***********:30016] added for master: redis://***********:30014 slot ranges: [[10923-16383]]
2025-09-04 16:37:27.221 | [34m INFO 81206[0;39m | [1;33mredisson-netty-1-19[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | master: redis://***********:30014 added for slot ranges: [[10923-16383]]
2025-09-04 16:37:27.243 | [34m INFO 81206[0;39m | [1;33mredisson-netty-1-23[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30015
2025-09-04 16:37:27.244 | [34m INFO 81206[0;39m | [1;33mredisson-netty-1-23[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | slaves: [redis://***********:30015] added for master: redis://***********:30013 slot ranges: [[5461-10922]]
2025-09-04 16:37:27.244 | [34m INFO 81206[0;39m | [1;33mredisson-netty-1-23[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | master: redis://***********:30013 added for slot ranges: [[5461-10922]]
2025-09-04 16:37:28.313 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mc.t.t.f.j.c.TsJacksonAutoConfiguration  [0;39m | [init][初始化 JsonUtils 成功]
2025-09-04 16:37:28.745 | [31m WARN 81206[0;39m | [1;33mmain[0;39m [1;32miguration$LoadBalancerCaffeineWarnLogger[0;39m | Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-09-04 16:37:28.892 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | initializer namespace from System Property :null
2025-09-04 16:37:28.893 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | initializer namespace from System Environment :null
2025-09-04 16:37:28.894 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | initializer namespace from System Property :null
2025-09-04 16:37:29.166 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | new ips(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"***********#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"***********","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-09-04 16:37:29.173 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | current ips:(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"***********#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"***********","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-09-04 16:37:29.189 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m | Tomcat started on port(s): 80 (http) with context path '/kepler/integration'
2025-09-04 16:37:29.194 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [BEAT] adding beat: BeatInfo{port=80, ip='************', weight=1.0, serviceName='DEFAULT_GROUP@@kepler-integration', cluster='DEFAULT', metadata={preserved.heart.beat.timeout=15000, preserved.ip.delete.timeout=30000, preserved.register.source=SPRING_CLOUD, preserved.heart.beat.interval=5000}, scheduled=false, period=5000, stopped=false} to beat map.
2025-09-04 16:37:29.195 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [REGISTER-SERVICE] dev registering service DEFAULT_GROUP@@kepler-integration with instance: Instance{instanceId='null', ip='************', port=80, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.heart.beat.timeout=15000, preserved.ip.delete.timeout=30000, preserved.register.source=SPRING_CLOUD, preserved.heart.beat.interval=5000}}
2025-09-04 16:37:29.203 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mc.a.c.n.registry.NacosServiceRegistry   [0;39m | nacos registry, DEFAULT_GROUP kepler-integration ************:80 register finished
2025-09-04 16:37:29.235 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mc.t.i.KeplerIntegrationServerApplication[0;39m | Started KeplerIntegrationServerApplication in 10.656 seconds (JVM running for 11.766)
2025-09-04 16:37:29.237 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 开始执行数据初始化任务...
2025-09-04 16:37:29.347 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 获取到初始化锁，开始执行数据初始化...
2025-09-04 16:37:29.348 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 开始检查应用系统变更表初始化状态...
2025-09-04 16:37:29.502 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 查询到应用系统数据 73 条，开始全量对比变更表...
2025-09-04 16:37:29.564 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 变更表中已存在版本为1的记录 73 条
2025-09-04 16:37:29.565 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 应用系统变更表初始化完成，跳过 73 条已存在记录，成功插入 0 条新记录
2025-09-04 16:37:29.565 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 开始检查应用程序变更表初始化状态...
2025-09-04 16:37:29.596 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 查询到应用程序数据 426 条，开始全量对比变更表...
2025-09-04 16:37:29.663 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 变更表中已存在版本为0的记录 426 条
2025-09-04 16:37:29.663 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 应用程序变更表初始化完成，跳过 426 条已存在记录，成功插入 0 条新记录
2025-09-04 16:37:29.664 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 数据初始化任务执行完成
2025-09-04 16:37:29.682 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 释放初始化锁
2025-09-04 16:37:29.710 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mc.a.n.client.config.impl.ClientWorker   [0;39m | [fixed-10.180.72.6_8848-dev] [subscribe] kepler-integration+DEFAULT_GROUP+dev
2025-09-04 16:37:29.711 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mc.a.nacos.client.config.impl.CacheData  [0;39m | [fixed-10.180.72.6_8848-dev] [add-listener] ok, tenant=dev, dataId=kepler-integration, group=DEFAULT_GROUP, cnt=1
2025-09-04 16:37:29.712 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mc.a.n.client.config.impl.ClientWorker   [0;39m | [fixed-10.180.72.6_8848-dev] [subscribe] kepler-integration.yaml+DEFAULT_GROUP+dev
2025-09-04 16:37:29.712 | [34m INFO 81206[0;39m | [1;33mmain[0;39m [1;32mc.a.nacos.client.config.impl.CacheData  [0;39m | [fixed-10.180.72.6_8848-dev] [add-listener] ok, tenant=dev, dataId=kepler-integration.yaml, group=DEFAULT_GROUP, cnt=1
2025-09-04 16:37:30.187 | [34m INFO 81206[0;39m | [1;33mcom.alibaba.nacos.client.naming.updater[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | new ips(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-09-04 16:37:30.188 | [34m INFO 81206[0;39m | [1;33mcom.alibaba.nacos.client.naming.updater[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | current ips:(2) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000},{"instanceId":"***********#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"***********","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-09-04 16:38:00.002 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.integration.task.DataShareSchedule  [0;39m | 执行应用系统全量共享任务
2025-09-04 16:38:00.267 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mo.a.k.clients.admin.AdminClientConfig   [0;39m | AdminClientConfig values: 
	bootstrap.servers = [tasp-traffic.trinasolar.com:31058, tasp-traffic.trinasolar.com:31059, tasp-traffic.trinasolar.com:31060]
	client.dns.lookup = use_all_dns_ips
	client.id = 
	connections.max.idle.ms = 300000
	default.api.timeout.ms = 60000
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retries = 2147483647
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = GSSAPI
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS

2025-09-04 16:38:00.337 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mo.a.kafka.common.utils.AppInfoParser    [0;39m | Kafka version: 3.1.2
2025-09-04 16:38:00.337 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mo.a.kafka.common.utils.AppInfoParser    [0;39m | Kafka commitId: f8c67dc3ae0a3265
2025-09-04 16:38:00.337 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mo.a.kafka.common.utils.AppInfoParser    [0;39m | Kafka startTimeMs: 1756975080333
2025-09-04 16:38:00.765 | [34m INFO 81206[0;39m | [1;33mkafka-admin-client-thread | adminclient-1[0;39m [1;32mo.a.kafka.common.utils.AppInfoParser    [0;39m | App info kafka.admin.client for adminclient-1 unregistered
2025-09-04 16:38:00.773 | [34m INFO 81206[0;39m | [1;33mkafka-admin-client-thread | adminclient-1[0;39m [1;32morg.apache.kafka.common.metrics.Metrics [0;39m | Metrics scheduler closed
2025-09-04 16:38:00.773 | [34m INFO 81206[0;39m | [1;33mkafka-admin-client-thread | adminclient-1[0;39m [1;32morg.apache.kafka.common.metrics.Metrics [0;39m | Closing reporter org.apache.kafka.common.metrics.JmxReporter
2025-09-04 16:38:00.773 | [34m INFO 81206[0;39m | [1;33mkafka-admin-client-thread | adminclient-1[0;39m [1;32morg.apache.kafka.common.metrics.Metrics [0;39m | Metrics reporters closed
2025-09-04 16:38:00.786 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=null
2025-09-04 16:38:00.787 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=de569c25-5c8e-4fcf-ae68-954dadcc28f2
2025-09-04 16:38:00.787 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=de569c25-5c8e-4fcf-ae68-954dadcc28f2
2025-09-04 16:38:00.788 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=de569c25-5c8e-4fcf-ae68-954dadcc28f2
2025-09-04 16:38:00.788 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=de569c25-5c8e-4fcf-ae68-954dadcc28f2
2025-09-04 16:38:00.789 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=de569c25-5c8e-4fcf-ae68-954dadcc28f2
2025-09-04 16:38:00.790 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=de569c25-5c8e-4fcf-ae68-954dadcc28f2
2025-09-04 16:38:00.790 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=de569c25-5c8e-4fcf-ae68-954dadcc28f2
2025-09-04 16:38:00.791 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=de569c25-5c8e-4fcf-ae68-954dadcc28f2
2025-09-04 16:38:00.791 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=de569c25-5c8e-4fcf-ae68-954dadcc28f2
2025-09-04 16:38:00.792 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=de569c25-5c8e-4fcf-ae68-954dadcc28f2
2025-09-04 16:38:00.792 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=de569c25-5c8e-4fcf-ae68-954dadcc28f2
2025-09-04 16:38:00.793 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=de569c25-5c8e-4fcf-ae68-954dadcc28f2
2025-09-04 16:38:00.793 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=de569c25-5c8e-4fcf-ae68-954dadcc28f2
2025-09-04 16:38:00.794 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=de569c25-5c8e-4fcf-ae68-954dadcc28f2
2025-09-04 16:38:00.802 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=de569c25-5c8e-4fcf-ae68-954dadcc28f2
2025-09-04 16:38:00.803 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=de569c25-5c8e-4fcf-ae68-954dadcc28f2
2025-09-04 16:38:00.803 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=de569c25-5c8e-4fcf-ae68-954dadcc28f2
2025-09-04 16:38:00.804 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=de569c25-5c8e-4fcf-ae68-954dadcc28f2
2025-09-04 16:38:00.804 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=de569c25-5c8e-4fcf-ae68-954dadcc28f2
2025-09-04 16:38:00.804 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=de569c25-5c8e-4fcf-ae68-954dadcc28f2
2025-09-04 16:38:00.805 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=de569c25-5c8e-4fcf-ae68-954dadcc28f2
2025-09-04 16:38:00.805 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=f93d426a-661c-4888-a2f8-e37e5d7b568f
2025-09-04 16:38:00.805 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=f43a5372-6e8f-42d8-9c21-0a2bd3fa47b2
2025-09-04 16:38:00.806 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=c9d629a3-12d2-4458-b98e-93b23841b305
2025-09-04 16:38:00.806 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=b4f62b75-df03-4431-a702-92efc567ec11
2025-09-04 16:38:00.806 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=10aa12e9-985a-4a7f-84c5-4775faf75f05
2025-09-04 16:38:00.807 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=075350d7-db60-4535-bcd7-d1254864fe54
2025-09-04 16:38:00.807 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=bb270488-348b-4b1b-a63b-922f0b36df1d
2025-09-04 16:38:00.807 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=800c39cc-3ba4-4078-a670-7197a7162e49
2025-09-04 16:38:00.808 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=afed6346-8554-41f0-b8b1-12ef17a3ffc8
2025-09-04 16:38:00.808 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=c752df5a-855a-4a46-9287-b23695542e58
2025-09-04 16:38:00.808 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=e80c63a3-1a7e-4d9a-ad78-eb88e96edc6a
2025-09-04 16:38:00.808 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=3fe82700-e11a-43d5-9ef4-9d3eeeb5fe70
2025-09-04 16:38:00.808 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=ccd459b5-4ae8-42c7-a711-f663ce3d60c6
2025-09-04 16:38:00.809 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=f7efa9a7-3218-4ad8-8c50-0d6e1ffa7268
2025-09-04 16:38:00.809 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=84ef8fb7-694d-4dc2-a6d9-ca149ea418f4
2025-09-04 16:38:00.809 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=testcase807-1
2025-09-04 16:38:00.809 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=testcase0807-1
2025-09-04 16:38:00.810 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=test0807-2-code
2025-09-04 16:38:00.810 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=test0807-3-code
2025-09-04 16:38:00.810 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=test0807-4-code
2025-09-04 16:38:00.810 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=test0807-6-code
2025-09-04 16:38:00.810 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=test0807-7-code
2025-09-04 16:38:00.811 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=testbugfix
2025-09-04 16:38:00.811 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=null
2025-09-04 16:38:00.811 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=null
2025-09-04 16:38:00.811 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=null
2025-09-04 16:38:00.811 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=null
2025-09-04 16:38:00.812 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=null
2025-09-04 16:38:00.812 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=null
2025-09-04 16:38:00.812 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=test
2025-09-04 16:38:00.812 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=null
2025-09-04 16:38:00.812 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=test
2025-09-04 16:38:00.813 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=da
2025-09-04 16:38:00.813 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=test
2025-09-04 16:38:00.813 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=test813-51
2025-09-04 16:38:00.813 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=test813-52
2025-09-04 16:38:00.813 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=test
2025-09-04 16:38:00.814 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=test
2025-09-04 16:38:00.814 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=123
2025-09-04 16:38:00.814 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=test814-1
2025-09-04 16:38:00.815 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=testcase
2025-09-04 16:38:00.815 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=testcase
2025-09-04 16:38:00.815 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=testcase
2025-09-04 16:38:00.816 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=testcase
2025-09-04 16:38:00.816 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=test182
2025-09-04 16:38:00.816 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=test183
2025-09-04 16:38:00.816 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=sdfsdf
2025-09-04 16:38:00.816 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=test903
2025-09-04 16:38:00.817 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=test9031
2025-09-04 16:38:00.817 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=testshare1
2025-09-04 16:38:00.817 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=testshare2
2025-09-04 16:38:00.831 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.integration.task.DataShareSchedule  [0;39m | 执行应用系统全量共享任务结束
2025-09-04 16:38:00.843 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.task.ScaScanDistributedTask       [0;39m | =======执行的SCA状态扫描任务======
2025-09-04 16:38:01.042 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.task.ScaScanDistributedTask       [0;39m | []
2025-09-04 16:38:27.294 | [34m INFO 81206[0;39m | [1;33mhttp-nio-80-exec-1[0;39m [1;32mo.a.c.c.C.[.[.[/kepler/integration]     [0;39m | Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-04 16:38:27.294 | [34m INFO 81206[0;39m | [1;33mhttp-nio-80-exec-1[0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Initializing Servlet 'dispatcherServlet'
2025-09-04 16:38:27.296 | [34m INFO 81206[0;39m | [1;33mhttp-nio-80-exec-1[0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed initialization in 1 ms
2025-09-04 16:38:27.407 | [34m INFO 81206[0;39m | [1;33mhttp-nio-80-exec-1[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/kepler/integration/product/list) 参数({noCache=1756975112900})]
2025-09-04 16:38:27.561 | [34m INFO 81206[0;39m | [1;33mhttp-nio-80-exec-1[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/kepler/integration/product/list) 耗时(154 ms)]
2025-09-04 16:39:00.010 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.task.ScaScanDistributedTask       [0;39m | =======执行的SCA状态扫描任务======
2025-09-04 16:39:00.107 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.task.ScaScanDistributedTask       [0;39m | []
2025-09-04 16:40:00.017 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.task.ScaScanDistributedTask       [0;39m | =======执行的SCA状态扫描任务======
2025-09-04 16:40:00.115 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.task.ScaScanDistributedTask       [0;39m | []
2025-09-04 16:41:00.007 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.task.ScaScanDistributedTask       [0;39m | =======执行的SCA状态扫描任务======
2025-09-04 16:41:00.119 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.task.ScaScanDistributedTask       [0;39m | []
2025-09-04 16:41:08.922 | [34m INFO 81206[0;39m | [1;33mhttp-nio-80-exec-3[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/kepler/integration/product/list) 参数({noCache=1756975274590})]
2025-09-04 16:41:09.001 | [34m INFO 81206[0;39m | [1;33mhttp-nio-80-exec-3[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/kepler/integration/product/list) 耗时(78 ms)]
2025-09-04 16:42:00.012 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.task.ScaScanDistributedTask       [0;39m | =======执行的SCA状态扫描任务======
2025-09-04 16:42:00.120 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.task.ScaScanDistributedTask       [0;39m | []
2025-09-04 16:42:11.651 | [34m INFO 81206[0;39m | [1;33mhttp-nio-80-exec-5[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/kepler/integration/api/sca/comps/list/20040) 参数({pageNo=1, pattern=, pageSize=10, source=0, type=1, taskId=20040})]
2025-09-04 16:42:11.919 | [34m INFO 81206[0;39m | [1;33mhttp-nio-80-exec-5[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/kepler/integration/api/sca/comps/list/20040) 耗时(267 ms)]
2025-09-04 16:42:15.533 | [34m INFO 81206[0;39m | [1;33mhttp-nio-80-exec-6[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/kepler/integration/api/sca/comps/list/20040) 参数({pageNo=1, pattern=, pageSize=10, source=0, type=0, taskId=20040})]
2025-09-04 16:42:15.749 | [34m INFO 81206[0;39m | [1;33mhttp-nio-80-exec-6[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/kepler/integration/api/sca/comps/list/20040) 耗时(215 ms)]
2025-09-04 16:43:00.011 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.task.ScaScanDistributedTask       [0;39m | =======执行的SCA状态扫描任务======
2025-09-04 16:43:00.117 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.task.ScaScanDistributedTask       [0;39m | []
2025-09-04 16:43:11.321 | [34m INFO 81206[0;39m | [1;33mhttp-nio-80-exec-7[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/kepler/integration/api/sca/comps/list/20040) 参数({pageNo=1, pattern=, pageSize=10, source=0, type=1, taskId=20040})]
2025-09-04 16:43:11.536 | [34m INFO 81206[0;39m | [1;33mhttp-nio-80-exec-7[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/kepler/integration/api/sca/comps/list/20040) 耗时(215 ms)]
2025-09-04 16:43:39.243 | [34m INFO 81206[0;39m | [1;33mhttp-nio-80-exec-8[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/kepler/integration/product/list) 参数({noCache=1756975424770})]
2025-09-04 16:43:39.303 | [34m INFO 81206[0;39m | [1;33mhttp-nio-80-exec-8[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/kepler/integration/product/list) 耗时(59 ms)]
2025-09-04 16:44:00.012 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.task.ScaScanDistributedTask       [0;39m | =======执行的SCA状态扫描任务======
2025-09-04 16:44:00.115 | [34m INFO 81206[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.task.ScaScanDistributedTask       [0;39m | []
2025-09-04 16:44:09.297 | [34m INFO 81206[0;39m | [1;33mhttp-nio-80-exec-9[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/kepler/integration/product/list) 参数({noCache=1756975450109})]
2025-09-04 16:44:09.355 | [34m INFO 81206[0;39m | [1;33mhttp-nio-80-exec-9[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/kepler/integration/product/list) 耗时(57 ms)]
2025-09-04 16:44:30.312 | [34m INFO 81206[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mc.a.n.client.config.impl.ClientWorker   [0;39m | [fixed-10.180.72.6_8848-dev] [polling-resp] config changed. dataId=kepler-integration.yaml, group=DEFAULT_GROUP, tenant=dev
2025-09-04 16:44:30.313 | [34m INFO 81206[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mc.a.n.client.config.impl.ClientWorker   [0;39m | get changedGroupKeys:[kepler-integration.yaml+DEFAULT_GROUP+dev]
2025-09-04 16:44:30.398 | [34m INFO 81206[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mc.a.n.client.config.impl.ClientWorker   [0;39m | [fixed-10.180.72.6_8848-dev] [data-received] dataId=kepler-integration.yaml, group=DEFAULT_GROUP, tenant=dev, md5=764df8555f239e0dedb502b0c63f8685, content=server:
  port: 80
  servlet:
    context-path: /kepler/integration
spring:
  application:
    name:..., type=yaml
2025-09-04 16:44:30.400 | [34m INFO 81206[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mc.a.nacos.client.config.impl.CacheData  [0;39m | [fixed-10.180.72.6_8848-dev] [notify-context] dataId=kepler-integration.yaml, group=DEFAULT_GROUP, md5=764df8555f239e0dedb502b0c63f8685
2025-09-04 16:44:30.731 | [34m INFO 81206[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mptablePropertiesBeanFactoryPostProcessor[0;39m | Post-processing PropertySource instances
2025-09-04 16:44:30.746 | [34m INFO 81206[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2025-09-04 16:44:30.746 | [34m INFO 81206[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 16:44:30.746 | [34m INFO 81206[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource refreshArgs [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 16:44:30.746 | [34m INFO 81206[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 16:44:30.746 | [34m INFO 81206[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 16:44:30.746 | [34m INFO 81206[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-09-04 16:44:30.746 | [34m INFO 81206[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-09-04 16:44:30.746 | [34m INFO 81206[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 16:44:30.746 | [34m INFO 81206[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 16:44:30.756 | [34m INFO 81206[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mc.u.j.filter.DefaultLazyPropertyFilter  [0;39m | Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-09-04 16:44:30.759 | [34m INFO 81206[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mc.u.j.r.DefaultLazyPropertyResolver     [0;39m | Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-09-04 16:44:30.759 | [34m INFO 81206[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mc.u.j.d.DefaultLazyPropertyDetector     [0;39m | Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-09-04 16:44:30.792 | [31m WARN 81206[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mc.a.c.n.c.NacosPropertySourceBuilder    [0;39m | Ignore the empty nacos configuration and get it based on dataId[kepler-integration] & group[DEFAULT_GROUP]
2025-09-04 16:44:30.821 | [31m WARN 81206[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mc.a.c.n.c.NacosPropertySourceBuilder    [0;39m | Ignore the empty nacos configuration and get it based on dataId[kepler-integration-dev.yaml] & group[DEFAULT_GROUP]
2025-09-04 16:44:30.827 | [31m WARN 81206[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mc.a.c.n.c.NacosPropertySourceBuilder    [0;39m | Ignore the empty nacos configuration and get it based on dataId[kepler-integration-uat.yaml] & group[DEFAULT_GROUP]
2025-09-04 16:44:30.827 | [34m INFO 81206[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mb.c.PropertySourceBootstrapConfiguration[0;39m | Located property source: [BootstrapPropertySource {name='bootstrapProperties-kepler-integration-uat.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kepler-integration-dev.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kepler-integration.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kepler-integration,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-application.yml,DEFAULT_GROUP'}]
2025-09-04 16:44:30.904 | [34m INFO 81206[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mEnableEncryptablePropertiesConfiguration[0;39m | Bootstraping jasypt-string-boot auto configuration in context: kepler-integration-1
2025-09-04 16:44:30.905 | [34m INFO 81206[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mo.s.boot.SpringApplication              [0;39m | The following 2 profiles are active: "dev", "uat"
2025-09-04 16:44:30.933 | [34m INFO 81206[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mo.s.boot.SpringApplication              [0;39m | Started application in 0.494 seconds (JVM running for 433.461)
2025-09-04 16:44:31.315 | [34m INFO 81206[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mu.j.c.RefreshScopeRefreshedEventListener[0;39m | Refreshing cached encryptable property sources
2025-09-04 16:44:31.316 | [34m INFO 81206[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mu.j.c.RefreshScopeRefreshedEventListener[0;39m | Refreshing cached encryptable property sources
2025-09-04 16:44:31.316 | [34m INFO 81206[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mo.s.c.e.event.RefreshEventListener      [0;39m | Refresh keys changed: [spring.redis.password, spring.datasource.username, oss.secret-key, schedule.datashare.system-full-cron, oss.access-key, spring.smart-redisson.password, spring.datasource.password]
2025-09-04 16:44:31.317 | [34m INFO 81206[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mc.a.nacos.client.config.impl.CacheData  [0;39m | [fixed-10.180.72.6_8848-dev] [notify-ok] dataId=kepler-integration.yaml, group=DEFAULT_GROUP, md5=764df8555f239e0dedb502b0c63f8685, listener=com.alibaba.cloud.nacos.refresh.NacosContextRefresher$1@33fd20a0 
2025-09-04 16:44:31.317 | [34m INFO 81206[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mc.a.nacos.client.config.impl.CacheData  [0;39m | [fixed-10.180.72.6_8848-dev] [notify-listener] time cost=917ms in ClientWorker, dataId=kepler-integration.yaml, group=DEFAULT_GROUP, md5=764df8555f239e0dedb502b0c63f8685, listener=com.alibaba.cloud.nacos.refresh.NacosContextRefresher$1@33fd20a0 
2025-09-04 16:44:36.914 | [31m WARN 81206[0;39m | [1;33mThread-16[0;39m [1;32mc.a.nacos.common.notify.NotifyCenter    [0;39m | [NotifyCenter] Start destroying Publisher
2025-09-04 16:44:36.914 | [31m WARN 81206[0;39m | [1;33mThread-2[0;39m [1;32mc.a.n.common.http.HttpClientBeanHolder  [0;39m | [HttpClientBeanHolder] Start destroying common HttpClient
2025-09-04 16:44:36.914 | [31m WARN 81206[0;39m | [1;33mThread-16[0;39m [1;32mc.a.nacos.common.notify.NotifyCenter    [0;39m | [NotifyCenter] Destruction of the end
2025-09-04 16:44:36.915 | [31m WARN 81206[0;39m | [1;33mThread-2[0;39m [1;32mc.a.n.common.http.HttpClientBeanHolder  [0;39m | [HttpClientBeanHolder] Destruction of the end
2025-09-04 16:44:36.948 | [34m INFO 81206[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.a.c.n.registry.NacosServiceRegistry   [0;39m | De-registering from Nacos Server now...
2025-09-04 16:44:36.949 | [34m INFO 81206[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [BEAT] removing beat: DEFAULT_GROUP@@kepler-integration:************:80 from beat map.
2025-09-04 16:44:36.949 | [34m INFO 81206[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [DEREGISTER-SERVICE] dev deregistering service DEFAULT_GROUP@@kepler-integration with instance: Instance{instanceId='null', ip='************', port=80, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={}}
2025-09-04 16:44:36.961 | [34m INFO 81206[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.a.c.n.registry.NacosServiceRegistry   [0;39m | De-registration finished.
2025-09-04 16:44:36.961 | [34m INFO 81206[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown begin
2025-09-04 16:44:39.973 | [34m INFO 81206[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown stop
2025-09-04 16:44:39.974 | [34m INFO 81206[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.core.HostReactor do shutdown begin
2025-09-04 16:44:40.760 | [34m INFO 81206[0;39m | [1;33mcom.alibaba.nacos.client.naming.updater[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | removed ips(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-09-04 16:44:40.766 | [34m INFO 81206[0;39m | [1;33mcom.alibaba.nacos.client.naming.updater[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | current ips:(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"***********#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"***********","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-09-04 16:44:40.768 | [34m INFO 81206[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.core.PushReceiver do shutdown begin
2025-09-04 16:44:43.779 | [34m INFO 81206[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.core.PushReceiver do shutdown stop
2025-09-04 16:44:43.779 | [34m INFO 81206[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin
2025-09-04 16:44:43.780 | [34m INFO 81206[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop
2025-09-04 16:44:43.780 | [34m INFO 81206[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.core.HostReactor do shutdown stop
2025-09-04 16:44:43.780 | [34m INFO 81206[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.net.NamingProxy do shutdown begin
2025-09-04 16:44:43.780 | [31m WARN 81206[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [NamingHttpClientManager] Start destroying NacosRestTemplate
2025-09-04 16:44:43.780 | [31m WARN 81206[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [NamingHttpClientManager] Destruction of the end
2025-09-04 16:44:43.781 | [34m INFO 81206[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.a.n.client.identify.CredentialWatcher [0;39m | [null] CredentialWatcher is stopped
2025-09-04 16:44:43.781 | [34m INFO 81206[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.a.n.client.identify.CredentialService [0;39m | [null] CredentialService is freed
2025-09-04 16:44:43.781 | [34m INFO 81206[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.net.NamingProxy do shutdown stop
2025-09-04 16:44:43.782 | [31m WARN 81206[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mo.s.b.f.support.DisposableBeanAdapter   [0;39m | Custom destroy method 'close' on bean with name 'nacosServiceRegistry' threw an exception: java.lang.NullPointerException
2025-09-04 16:44:43.847 | [34m INFO 81206[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource start closing ....
2025-09-04 16:44:43.850 | [34m INFO 81206[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1} closing ...
2025-09-04 16:44:43.859 | [34m INFO 81206[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1} closed
2025-09-04 16:44:43.859 | [34m INFO 81206[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.b.d.d.d.DefaultDataSourceDestroyer    [0;39m | dynamic-datasource close the datasource named [master] success,
2025-09-04 16:44:43.859 | [34m INFO 81206[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource all closed success,bye
2025-09-04 16:44:49.391 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mptablePropertiesBeanFactoryPostProcessor[0;39m | Post-processing PropertySource instances
2025-09-04 16:44:49.455 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2025-09-04 16:44:49.456 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 16:44:49.457 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 16:44:49.457 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 16:44:49.457 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-09-04 16:44:49.457 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-09-04 16:44:49.457 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 16:44:49.457 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 16:44:49.502 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mc.u.j.filter.DefaultLazyPropertyFilter  [0;39m | Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-09-04 16:44:49.506 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mc.u.j.r.DefaultLazyPropertyResolver     [0;39m | Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-09-04 16:44:49.507 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mc.u.j.d.DefaultLazyPropertyDetector     [0;39m | Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-09-04 16:44:49.947 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mc.a.n.c.c.impl.LocalConfigInfoProcessor [0;39m | LOCAL_SNAPSHOT_PATH:/Users/<USER>/nacos/config
2025-09-04 16:44:49.978 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mc.a.nacos.client.config.utils.JvmUtil   [0;39m | isMultiInstance:false
2025-09-04 16:44:50.003 | [31m WARN 82922[0;39m | [1;33mmain[0;39m [1;32mc.a.c.n.c.NacosPropertySourceBuilder    [0;39m | Ignore the empty nacos configuration and get it based on dataId[kepler-integration] & group[DEFAULT_GROUP]
2025-09-04 16:44:50.018 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mb.c.PropertySourceBootstrapConfiguration[0;39m | Located property source: [BootstrapPropertySource {name='bootstrapProperties-kepler-integration.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kepler-integration,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-application.yml,DEFAULT_GROUP'}]
2025-09-04 16:44:50.045 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mEnableEncryptablePropertiesConfiguration[0;39m | Bootstraping jasypt-string-boot auto configuration in context: kepler-integration-1
2025-09-04 16:44:50.046 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mc.t.i.KeplerIntegrationServerApplication[0;39m | The following 2 profiles are active: "dev", "uat"
2025-09-04 16:44:51.191 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32m.s.d.r.c.RepositoryConfigurationDelegate[0;39m | Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-04 16:44:51.194 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32m.s.d.r.c.RepositoryConfigurationDelegate[0;39m | Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-04 16:44:51.257 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32m.s.d.r.c.RepositoryConfigurationDelegate[0;39m | Finished Spring Data repository scanning in 42 ms. Found 0 Redis repository interfaces.
2025-09-04 16:44:51.528 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mo.s.cloud.context.scope.GenericScope    [0;39m | BeanFactory id=f8c5ab0f-68e8-3840-9720-c2a4515bce20
2025-09-04 16:44:51.588 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mptablePropertiesBeanFactoryPostProcessor[0;39m | Post-processing PropertySource instances
2025-09-04 16:44:51.601 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrapProperties-kepler-integration.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-09-04 16:44:51.601 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrapProperties-kepler-integration,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-09-04 16:44:51.601 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrapProperties-application.yml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-09-04 16:44:51.602 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2025-09-04 16:44:51.602 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-09-04 16:44:51.602 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-09-04 16:44:51.602 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 16:44:51.602 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 16:44:51.602 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-09-04 16:44:51.602 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-09-04 16:44:51.602 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 16:44:51.603 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 16:44:51.613 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mc.u.j.filter.DefaultLazyPropertyFilter  [0;39m | Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-09-04 16:44:51.619 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mc.u.j.r.DefaultLazyPropertyResolver     [0;39m | Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-09-04 16:44:51.620 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mc.u.j.d.DefaultLazyPropertyDetector     [0;39m | Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-09-04 16:44:51.780 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-04 16:44:51.783 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-04 16:44:51.784 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$662/0x00000008005aac40] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-04 16:44:51.786 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-04 16:44:52.232 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m | Tomcat initialized with port(s): 80 (http)
2025-09-04 16:44:52.243 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mo.apache.catalina.core.StandardService  [0;39m | Starting service [Tomcat]
2025-09-04 16:44:52.244 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32morg.apache.catalina.core.StandardEngine [0;39m | Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-09-04 16:44:52.401 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mo.a.c.c.C.[.[.[/kepler/integration]     [0;39m | Initializing Spring embedded WebApplicationContext
2025-09-04 16:44:52.402 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mw.s.c.ServletWebServerApplicationContext[0;39m | Root WebApplicationContext: initialization completed in 2335 ms
2025-09-04 16:44:52.737 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mf.a.AutowiredAnnotationBeanPostProcessor[0;39m | Autowired annotation is not supported on static fields: private static java.lang.String com.trinasolar.integration.config.security.aes.AESConfig.key
2025-09-04 16:44:52.812 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m | mybatis plus join properties config complete
2025-09-04 16:44:53.212 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1,master} inited
2025-09-04 16:44:53.213 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource - add a datasource named [master] success
2025-09-04 16:44:53.214 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-09-04 16:44:53.271 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m | mybatis plus join SqlInjector init
2025-09-04 16:44:55.450 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-09-04 16:44:55.459 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
2025-09-04 16:44:55.459 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.keyObtentionIterations, using default value: 1000
2025-09-04 16:44:55.459 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.poolSize, using default value: 1
2025-09-04 16:44:55.459 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.providerName, using default value: null
2025-09-04 16:44:55.459 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.providerClassName, using default value: null
2025-09-04 16:44:55.459 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.saltGeneratorClassname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-09-04 16:44:55.460 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.ivGeneratorClassname, using default value: org.jasypt.salt.NoOpIVGenerator
2025-09-04 16:44:55.461 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.stringOutputType, using default value: base64
2025-09-04 16:44:55.743 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32morg.redisson.Version                    [0;39m | Redisson 3.41.0
2025-09-04 16:44:55.828 | [31m WARN 82922[0;39m | [1;33mmain[0;39m [1;32mi.n.r.d.DnsServerAddressStreamProviders [0;39m | Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-09-04 16:44:56.127 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | Redis cluster nodes configuration got from ***********/***********:30012:
461896f00a0c24e3126dc34638854ff6422fb244 ***********:30012@30018 myself,master - 0 1756975495000 1 connected 0-5460
e0b782a8e69537dcc37ea3d2a72258a45ddb2d53 ***********:30014@30020 master - 0 1756975496000 3 connected 10923-16383
93167ed470a2aaf64573d363e6bd6554e03bc4d9 ***********:30013@30019 master - 0 1756975494000 2 connected 5461-10922
0d5fd0961bdeb1f6249808fe9218ccbf5707f630 ***********:30017@30023 slave 461896f00a0c24e3126dc34638854ff6422fb244 0 1756975497797 1 connected
7814d1a5f8f3a16c9b5a1a4212329b85b44e2027 ***********:30015@30021 slave 93167ed470a2aaf64573d363e6bd6554e03bc4d9 0 1756975495000 2 connected
19e5162fb0b4f5bd64d046f5a9a2c2b1db62918e ***********:30016@30022 slave e0b782a8e69537dcc37ea3d2a72258a45ddb2d53 0 1756975496792 3 connected

2025-09-04 16:44:56.186 | [34m INFO 82922[0;39m | [1;33mredisson-netty-1-17[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30012
2025-09-04 16:44:56.191 | [34m INFO 82922[0;39m | [1;33mredisson-netty-1-19[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30014
2025-09-04 16:44:56.201 | [34m INFO 82922[0;39m | [1;33mredisson-netty-1-24[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30013
2025-09-04 16:44:56.512 | [34m INFO 82922[0;39m | [1;33mredisson-netty-1-21[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30012
2025-09-04 16:44:56.545 | [34m INFO 82922[0;39m | [1;33mredisson-netty-1-3[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30013
2025-09-04 16:44:56.558 | [34m INFO 82922[0;39m | [1;33mredisson-netty-1-7[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30014
2025-09-04 16:44:56.564 | [34m INFO 82922[0;39m | [1;33mredisson-netty-1-10[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30017
2025-09-04 16:44:56.574 | [34m INFO 82922[0;39m | [1;33mredisson-netty-1-13[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30015
2025-09-04 16:44:56.585 | [34m INFO 82922[0;39m | [1;33mredisson-netty-1-21[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30016
2025-09-04 16:44:56.928 | [34m INFO 82922[0;39m | [1;33mredisson-netty-1-13[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30017
2025-09-04 16:44:56.930 | [34m INFO 82922[0;39m | [1;33mredisson-netty-1-13[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | slaves: [redis://***********:30017] added for master: redis://***********:30012 slot ranges: [[0-5460]]
2025-09-04 16:44:56.930 | [34m INFO 82922[0;39m | [1;33mredisson-netty-1-13[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | master: redis://***********:30012 added for slot ranges: [[0-5460]]
2025-09-04 16:44:56.945 | [34m INFO 82922[0;39m | [1;33mredisson-netty-1-20[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30015
2025-09-04 16:44:56.946 | [34m INFO 82922[0;39m | [1;33mredisson-netty-1-20[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | slaves: [redis://***********:30015] added for master: redis://***********:30013 slot ranges: [[5461-10922]]
2025-09-04 16:44:56.946 | [34m INFO 82922[0;39m | [1;33mredisson-netty-1-20[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | master: redis://***********:30013 added for slot ranges: [[5461-10922]]
2025-09-04 16:44:56.958 | [34m INFO 82922[0;39m | [1;33mredisson-netty-1-22[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30016
2025-09-04 16:44:56.959 | [34m INFO 82922[0;39m | [1;33mredisson-netty-1-22[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | slaves: [redis://***********:30016] added for master: redis://***********:30014 slot ranges: [[10923-16383]]
2025-09-04 16:44:56.959 | [34m INFO 82922[0;39m | [1;33mredisson-netty-1-22[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | master: redis://***********:30014 added for slot ranges: [[10923-16383]]
2025-09-04 16:44:58.054 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mc.t.t.f.j.c.TsJacksonAutoConfiguration  [0;39m | [init][初始化 JsonUtils 成功]
2025-09-04 16:44:58.510 | [31m WARN 82922[0;39m | [1;33mmain[0;39m [1;32miguration$LoadBalancerCaffeineWarnLogger[0;39m | Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-09-04 16:44:58.649 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | initializer namespace from System Property :null
2025-09-04 16:44:58.650 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | initializer namespace from System Environment :null
2025-09-04 16:44:58.651 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | initializer namespace from System Property :null
2025-09-04 16:44:58.905 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | new ips(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"***********#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"***********","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-09-04 16:44:58.912 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | current ips:(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"***********#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"***********","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-09-04 16:44:58.930 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m | Tomcat started on port(s): 80 (http) with context path '/kepler/integration'
2025-09-04 16:44:58.935 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [BEAT] adding beat: BeatInfo{port=80, ip='************', weight=1.0, serviceName='DEFAULT_GROUP@@kepler-integration', cluster='DEFAULT', metadata={preserved.heart.beat.timeout=15000, preserved.ip.delete.timeout=30000, preserved.register.source=SPRING_CLOUD, preserved.heart.beat.interval=5000}, scheduled=false, period=5000, stopped=false} to beat map.
2025-09-04 16:44:58.936 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [REGISTER-SERVICE] dev registering service DEFAULT_GROUP@@kepler-integration with instance: Instance{instanceId='null', ip='************', port=80, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.heart.beat.timeout=15000, preserved.ip.delete.timeout=30000, preserved.register.source=SPRING_CLOUD, preserved.heart.beat.interval=5000}}
2025-09-04 16:44:58.943 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mc.a.c.n.registry.NacosServiceRegistry   [0;39m | nacos registry, DEFAULT_GROUP kepler-integration ************:80 register finished
2025-09-04 16:44:58.969 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mc.t.i.KeplerIntegrationServerApplication[0;39m | Started KeplerIntegrationServerApplication in 10.63 seconds (JVM running for 11.361)
2025-09-04 16:44:58.971 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 开始执行数据初始化任务...
2025-09-04 16:44:59.016 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 获取到初始化锁，开始执行数据初始化...
2025-09-04 16:44:59.016 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 开始检查应用系统变更表初始化状态...
2025-09-04 16:44:59.172 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 查询到应用系统数据 73 条，开始全量对比变更表...
2025-09-04 16:44:59.212 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 变更表中已存在版本为1的记录 73 条
2025-09-04 16:44:59.213 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 应用系统变更表初始化完成，跳过 73 条已存在记录，成功插入 0 条新记录
2025-09-04 16:44:59.213 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 开始检查应用程序变更表初始化状态...
2025-09-04 16:44:59.240 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 查询到应用程序数据 426 条，开始全量对比变更表...
2025-09-04 16:44:59.291 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 变更表中已存在版本为0的记录 426 条
2025-09-04 16:44:59.292 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 应用程序变更表初始化完成，跳过 426 条已存在记录，成功插入 0 条新记录
2025-09-04 16:44:59.292 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 数据初始化任务执行完成
2025-09-04 16:44:59.318 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 释放初始化锁
2025-09-04 16:44:59.335 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mc.a.n.client.config.impl.ClientWorker   [0;39m | [fixed-10.180.72.6_8848-dev] [subscribe] kepler-integration+DEFAULT_GROUP+dev
2025-09-04 16:44:59.336 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mc.a.nacos.client.config.impl.CacheData  [0;39m | [fixed-10.180.72.6_8848-dev] [add-listener] ok, tenant=dev, dataId=kepler-integration, group=DEFAULT_GROUP, cnt=1
2025-09-04 16:44:59.337 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mc.a.n.client.config.impl.ClientWorker   [0;39m | [fixed-10.180.72.6_8848-dev] [subscribe] kepler-integration.yaml+DEFAULT_GROUP+dev
2025-09-04 16:44:59.337 | [34m INFO 82922[0;39m | [1;33mmain[0;39m [1;32mc.a.nacos.client.config.impl.CacheData  [0;39m | [fixed-10.180.72.6_8848-dev] [add-listener] ok, tenant=dev, dataId=kepler-integration.yaml, group=DEFAULT_GROUP, cnt=1
2025-09-04 16:44:59.925 | [34m INFO 82922[0;39m | [1;33mcom.alibaba.nacos.client.naming.updater[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | new ips(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-09-04 16:44:59.926 | [34m INFO 82922[0;39m | [1;33mcom.alibaba.nacos.client.naming.updater[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | current ips:(2) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000},{"instanceId":"***********#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"***********","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-09-04 16:45:00.020 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.task.ScaScanDistributedTask       [0;39m | =======执行的SCA状态扫描任务======
2025-09-04 16:45:00.303 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.task.ScaScanDistributedTask       [0;39m | []
2025-09-04 16:45:05.472 | [34m INFO 82922[0;39m | [1;33mhttp-nio-80-exec-1[0;39m [1;32mo.a.c.c.C.[.[.[/kepler/integration]     [0;39m | Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-04 16:45:05.472 | [34m INFO 82922[0;39m | [1;33mhttp-nio-80-exec-1[0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Initializing Servlet 'dispatcherServlet'
2025-09-04 16:45:05.474 | [34m INFO 82922[0;39m | [1;33mhttp-nio-80-exec-1[0;39m [1;32mo.s.web.servlet.DispatcherServlet       [0;39m | Completed initialization in 2 ms
2025-09-04 16:45:05.551 | [34m INFO 82922[0;39m | [1;33mhttp-nio-80-exec-1[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/kepler/integration/product/list) 无参数]
2025-09-04 16:45:05.778 | [34m INFO 82922[0;39m | [1;33mhttp-nio-80-exec-1[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/kepler/integration/product/list) 耗时(226 ms)]
2025-09-04 16:46:00.013 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.task.ScaScanDistributedTask       [0;39m | =======执行的SCA状态扫描任务======
2025-09-04 16:46:00.126 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.task.ScaScanDistributedTask       [0;39m | []
2025-09-04 16:46:00.141 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.integration.task.DataShareSchedule  [0;39m | 执行应用系统全量共享任务
2025-09-04 16:46:00.274 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mo.a.k.clients.admin.AdminClientConfig   [0;39m | AdminClientConfig values: 
	bootstrap.servers = [tasp-traffic.trinasolar.com:31058, tasp-traffic.trinasolar.com:31059, tasp-traffic.trinasolar.com:31060]
	client.dns.lookup = use_all_dns_ips
	client.id = 
	connections.max.idle.ms = 300000
	default.api.timeout.ms = 60000
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retries = 2147483647
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = GSSAPI
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS

2025-09-04 16:46:00.341 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mo.a.kafka.common.utils.AppInfoParser    [0;39m | Kafka version: 3.1.2
2025-09-04 16:46:00.341 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mo.a.kafka.common.utils.AppInfoParser    [0;39m | Kafka commitId: f8c67dc3ae0a3265
2025-09-04 16:46:00.341 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mo.a.kafka.common.utils.AppInfoParser    [0;39m | Kafka startTimeMs: 1756975560339
2025-09-04 16:46:00.994 | [34m INFO 82922[0;39m | [1;33mkafka-admin-client-thread | adminclient-1[0;39m [1;32mo.a.kafka.common.utils.AppInfoParser    [0;39m | App info kafka.admin.client for adminclient-1 unregistered
2025-09-04 16:46:00.999 | [34m INFO 82922[0;39m | [1;33mkafka-admin-client-thread | adminclient-1[0;39m [1;32morg.apache.kafka.common.metrics.Metrics [0;39m | Metrics scheduler closed
2025-09-04 16:46:00.999 | [34m INFO 82922[0;39m | [1;33mkafka-admin-client-thread | adminclient-1[0;39m [1;32morg.apache.kafka.common.metrics.Metrics [0;39m | Closing reporter org.apache.kafka.common.metrics.JmxReporter
2025-09-04 16:46:00.999 | [34m INFO 82922[0;39m | [1;33mkafka-admin-client-thread | adminclient-1[0;39m [1;32morg.apache.kafka.common.metrics.Metrics [0;39m | Metrics reporters closed
2025-09-04 16:46:01.010 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | rawBusinessData={"id":1,"createdTime":1750303827000,"creatorId":1,"creatorName":"管理员","updatedTime":1755077036000,"updaterId":1,"updaterName":"管理员","tenantId":1,"cnName":"应用服务平台","cnSimpleName":"应用服务平台","enName":"tasp","enSimpleName":"tasp","cloudName":"tasp","cloudAppId":"topo-34e4e1d5b75646d3-kepler=harmony-kepler-test","cloudProjectId":"34e4e1d5b75646d3","cloudProjectName":"tasp","cloudProjectAliasName":"应用服务平台","cloudTenantId":"e30e540af9c04d8e","cloudTenantName":"harmony","cloudTenantAliasName":"谐云","cloudNamespaceId":"harmony-kepler-test","cloudNamespaceName":"应用服务平台","cloudClusterId":null,"stageStatus":"Constructing","lockFlag":"0","appUrl":"http://10.180.39.245,https://tasp-dev.trinasolar.com,https://tasp-uat.trinasolar.com","remark":"天合光能应用服务平台（TASP）是为天合应用系统建设者、管理者和IT管理者提供的一站式应用建设与集成管理平台。平台以提升应用系统建设与管理能效为目标，通过提供丰富的共享组件、标准的PaaS服务、系统化的交付文档库和自动化的应用全生命周期管理，减少研发人员学习成本、提高应用研发和部署效率，提升应用建设质量与效能，助力天合数字化转型。","detailPicUrl":"","logoUrl":"https://tos.trinasolar.com/n5r00e239du8tvtk1hw5o4/app/logo.svg","titleType":null,"imgTitle":null,"title":"应用服务平台","subTitle":null,"bgImage":null,"showType":null,"alignment":null,"appStyle":null,"appType":"1141543926008647681","isOuter":null,"expireTime":null,"urlType":0,"configItems":null,"homePage":null,"showHomepage":0,"manageUrl":"","isMobile":0,"isManagePcAppMenu":0,"contraction":0,"platformEnName":null,"platformCnName":null,"ieSupported":0,"ieTip":null,"isControlFP":null,"isControlDP":null,"isContainerApp":null,"tenantShow":null,"businessDomain":"ipd","tenantGroup":null,"isChangeTenant":null,"appSysType":"1171262252406378535","appGroup":"8","appServiceLevel":"1","constructionType":"1","devLanguage":"1171262252406378573,1171262252406378576","teamName":"ipd","gitlab":null,"devops":null,"apm":null,"code":null,"accessUrl":"https://tasp-test.trinasolar.com","manualUrl":null,"actuallyTime":null,"cluster":null,"relationSystem":null,"nodeSpec":null,"version":"V1","appDomain":null,"appName":null,"productLine":null,"bizUnit":"0","bizScopeType":"0","bizScope":"0","syncNextSystem":null,"externalProductName":null,"productVendor":null,"implVendor":null,"licenseMode":null,"licenseDescription":null,"priorityLevel":"0","serviceHours":"","description":null,"flowDomain":"","mobile":false}
2025-09-04 16:46:01.013 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | filteredBusinessData={}
2025-09-04 16:46:01.013 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=null
2025-09-04 16:46:01.014 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | rawBusinessData={"id":1747463606888,"createdTime":1747463606000,"creatorId":1,"creatorName":"admin","updatedTime":1754966977000,"updaterId":1,"updaterName":"admin","tenantId":1,"cnName":"产供销","cnSimpleName":"产供销","enName":"scp","enSimpleName":"scp","cloudName":null,"cloudAppId":null,"cloudProjectId":null,"cloudProjectName":null,"cloudProjectAliasName":null,"cloudTenantId":null,"cloudTenantName":null,"cloudTenantAliasName":null,"cloudNamespaceId":null,"cloudNamespaceName":null,"cloudClusterId":null,"stageStatus":"Constructing","lockFlag":"0","appUrl":"scp.trinasolar.com","remark":"产供销数字化平台，打通销售、计划、生产、采购的一体化平台，还包含认证管理、特殊需求管理等业务","detailPicUrl":null,"logoUrl":"https://tos.trinasolar.com/n5r00e239du8tvtk1hw5o4/a4c8f566-8cdf-4761-8568-5e50c8fc37b4.jpeg","titleType":null,"imgTitle":null,"title":null,"subTitle":null,"bgImage":null,"showType":null,"alignment":null,"appStyle":null,"appType":null,"isOuter":0,"expireTime":null,"urlType":1,"configItems":null,"homePage":null,"showHomepage":0,"manageUrl":null,"isMobile":0,"isManagePcAppMenu":0,"contraction":0,"platformEnName":null,"platformCnName":null,"ieSupported":0,"ieTip":null,"isControlFP":0,"isControlDP":0,"isContainerApp":0,"tenantShow":1,"businessDomain":"isc","tenantGroup":null,"isChangeTenant":0,"appSysType":"1171262252406378535","appGroup":"7","appServiceLevel":"1","constructionType":"2","devLanguage":"1171262252406378573,1171262252406378576","teamName":"isc","gitlab":true,"devops":true,"apm":false,"code":"de569c25-5c8e-4fcf-ae68-954dadcc28f2","accessUrl":"scp.trinasolar.com","manualUrl":"","actuallyTime":null,"cluster":null,"relationSystem":null,"nodeSpec":null,"version":"V1","appDomain":null,"appName":null,"productLine":null,"bizUnit":"0","bizScopeType":"0","bizScope":"0","syncNextSystem":null,"externalProductName":null,"productVendor":null,"implVendor":null,"licenseMode":null,"licenseDescription":null,"priorityLevel":"0","serviceHours":"","description":null,"flowDomain":"","mobile":false}
2025-09-04 16:46:01.014 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | filteredBusinessData={}
2025-09-04 16:46:01.014 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=de569c25-5c8e-4fcf-ae68-954dadcc28f2
2025-09-04 16:46:01.015 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | rawBusinessData={"id":1747463606902,"createdTime":1747463606000,"creatorId":1,"creatorName":"admin","updatedTime":1754966977000,"updaterId":1,"updaterName":"admin","tenantId":1,"cnName":"至尊宝-中国区分销平台","cnSimpleName":"至尊宝-中国区分销平台","enName":"trinax","enSimpleName":"trinax","cloudName":null,"cloudAppId":null,"cloudProjectId":null,"cloudProjectName":null,"cloudProjectAliasName":null,"cloudTenantId":null,"cloudTenantName":null,"cloudTenantAliasName":null,"cloudNamespaceId":null,"cloudNamespaceName":null,"cloudClusterId":null,"stageStatus":"Constructing","lockFlag":"0","appUrl":"trinax.trinasolar.com","remark":"中国区分销商渠道管理","detailPicUrl":null,"logoUrl":"https://tos.trinasolar.com/n5r00e239du8tvtk1hw5o4/a4c8f566-8cdf-4761-8568-5e50c8fc37b4.jpeg","titleType":null,"imgTitle":null,"title":null,"subTitle":null,"bgImage":null,"showType":null,"alignment":null,"appStyle":null,"appType":null,"isOuter":0,"expireTime":null,"urlType":1,"configItems":null,"homePage":null,"showHomepage":0,"manageUrl":null,"isMobile":0,"isManagePcAppMenu":0,"contraction":0,"platformEnName":null,"platformCnName":null,"ieSupported":0,"ieTip":null,"isControlFP":0,"isControlDP":0,"isContainerApp":0,"tenantShow":1,"businessDomain":"ltc","tenantGroup":null,"isChangeTenant":0,"appSysType":"1171262252406378535,1171262252406378536,1171262252406378534,1171262252406378537","appGroup":"7","appServiceLevel":"1","constructionType":"2","devLanguage":"1171262252406378573,1171262252406378576","teamName":"ltc","gitlab":true,"devops":true,"apm":false,"code":"de569c25-5c8e-4fcf-ae68-954dadcc28f2","accessUrl":"trinax.trinasolar.com","manualUrl":"trinax.trinasolar.com","actuallyTime":null,"cluster":null,"relationSystem":null,"nodeSpec":null,"version":"V1","appDomain":null,"appName":null,"productLine":null,"bizUnit":"0","bizScopeType":"0","bizScope":"0","syncNextSystem":null,"externalProductName":null,"productVendor":null,"implVendor":null,"licenseMode":null,"licenseDescription":null,"priorityLevel":"0","serviceHours":"","description":null,"flowDomain":"","mobile":false}
2025-09-04 16:46:01.015 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | filteredBusinessData={}
2025-09-04 16:46:01.015 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=de569c25-5c8e-4fcf-ae68-954dadcc28f2
2025-09-04 16:46:01.015 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | rawBusinessData={"id":1747463606905,"createdTime":1747463606000,"creatorId":1,"creatorName":"admin","updatedTime":1754966977000,"updaterId":1,"updaterName":"admin","tenantId":1,"cnName":"欧洲分布式","cnSimpleName":"欧洲分布式","enName":"trina-buy","enSimpleName":"trina-buy","cloudName":null,"cloudAppId":null,"cloudProjectId":null,"cloudProjectName":null,"cloudProjectAliasName":null,"cloudTenantId":null,"cloudTenantName":null,"cloudTenantAliasName":null,"cloudNamespaceId":null,"cloudNamespaceName":null,"cloudClusterId":null,"stageStatus":"Constructing","lockFlag":"0","appUrl":"mytrina-eu.trinasolar.com","remark":"为欧洲分布式客户提供在线下单管理能力, 提升客户体验与交易效率。","detailPicUrl":null,"logoUrl":"https://tos.trinasolar.com/n5r00e239du8tvtk1hw5o4/a4c8f566-8cdf-4761-8568-5e50c8fc37b4.jpeg","titleType":null,"imgTitle":null,"title":null,"subTitle":null,"bgImage":null,"showType":null,"alignment":null,"appStyle":null,"appType":null,"isOuter":0,"expireTime":null,"urlType":1,"configItems":null,"homePage":null,"showHomepage":0,"manageUrl":null,"isMobile":0,"isManagePcAppMenu":0,"contraction":0,"platformEnName":null,"platformCnName":null,"ieSupported":0,"ieTip":null,"isControlFP":0,"isControlDP":0,"isContainerApp":0,"tenantShow":1,"businessDomain":"ltc","tenantGroup":null,"isChangeTenant":0,"appSysType":"1171262252406378535,1171262252406378536","appGroup":"7","appServiceLevel":"1","constructionType":"2","devLanguage":"1171262252406378573,1171262252406378576","teamName":"ltc","gitlab":true,"devops":true,"apm":false,"code":"de569c25-5c8e-4fcf-ae68-954dadcc28f2","accessUrl":"mytrina-eu.trinasolar.com","manualUrl":"","actuallyTime":null,"cluster":null,"relationSystem":null,"nodeSpec":null,"version":"V1","appDomain":null,"appName":null,"productLine":null,"bizUnit":"0","bizScopeType":"0","bizScope":"0","syncNextSystem":null,"externalProductName":null,"productVendor":null,"implVendor":null,"licenseMode":null,"licenseDescription":null,"priorityLevel":"0","serviceHours":"","description":null,"flowDomain":"","mobile":false}
2025-09-04 16:46:01.016 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | filteredBusinessData={}
2025-09-04 16:46:01.016 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=de569c25-5c8e-4fcf-ae68-954dadcc28f2
2025-09-04 16:46:01.016 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | rawBusinessData={"id":1747463606907,"createdTime":1747463606000,"creatorId":1,"creatorName":"admin","updatedTime":1754966977000,"updaterId":1,"updaterName":"admin","tenantId":1,"cnName":"客户服务平台","cnSimpleName":"客户服务平台","enName":"csp","enSimpleName":"csp","cloudName":null,"cloudAppId":null,"cloudProjectId":null,"cloudProjectName":null,"cloudProjectAliasName":null,"cloudTenantId":null,"cloudTenantName":null,"cloudTenantAliasName":null,"cloudNamespaceId":null,"cloudNamespaceName":null,"cloudClusterId":null,"stageStatus":"Constructing","lockFlag":"0","appUrl":"https://customerservice.trinasolar.com/#/home","remark":"全球RTS及GCS进行客诉处理的后台管理平台。【业务系统负责人-仇叶飞-上生产后更新】","detailPicUrl":null,"logoUrl":"https://tos.trinasolar.com/n5r00e239du8tvtk1hw5o4/a4c8f566-8cdf-4761-8568-5e50c8fc37b4.jpeg","titleType":null,"imgTitle":null,"title":null,"subTitle":null,"bgImage":null,"showType":null,"alignment":null,"appStyle":null,"appType":null,"isOuter":0,"expireTime":null,"urlType":1,"configItems":null,"homePage":null,"showHomepage":0,"manageUrl":null,"isMobile":0,"isManagePcAppMenu":0,"contraction":0,"platformEnName":null,"platformCnName":null,"ieSupported":0,"ieTip":null,"isControlFP":0,"isControlDP":0,"isContainerApp":0,"tenantShow":1,"businessDomain":"ltc","tenantGroup":null,"isChangeTenant":0,"appSysType":"1171262252406378535","appGroup":"7","appServiceLevel":"1","constructionType":"0","devLanguage":"1171262252406378573,1171262252406378576","teamName":"ltc","gitlab":true,"devops":true,"apm":false,"code":"de569c25-5c8e-4fcf-ae68-954dadcc28f2","accessUrl":"https://customerservice.trinasolar.com/#/home","manualUrl":"","actuallyTime":null,"cluster":null,"relationSystem":null,"nodeSpec":null,"version":"V1","appDomain":null,"appName":null,"productLine":null,"bizUnit":"0","bizScopeType":"0","bizScope":"0","syncNextSystem":null,"externalProductName":null,"productVendor":null,"implVendor":null,"licenseMode":null,"licenseDescription":null,"priorityLevel":"0","serviceHours":"","description":null,"flowDomain":"","mobile":false}
2025-09-04 16:46:01.017 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | filteredBusinessData={}
2025-09-04 16:46:01.017 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=de569c25-5c8e-4fcf-ae68-954dadcc28f2
2025-09-04 16:46:01.017 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | rawBusinessData={"id":1747463606917,"createdTime":1747463606000,"creatorId":1,"creatorName":"admin","updatedTime":1754966977000,"updaterId":1,"updaterName":"admin","tenantId":1,"cnName":"特殊需求","cnSimpleName":"特殊需求","enName":"scr","enSimpleName":"scr","cloudName":null,"cloudAppId":null,"cloudProjectId":null,"cloudProjectName":null,"cloudProjectAliasName":null,"cloudTenantId":null,"cloudTenantName":null,"cloudTenantAliasName":null,"cloudNamespaceId":null,"cloudNamespaceName":null,"cloudClusterId":null,"stageStatus":"Constructing","lockFlag":"0","appUrl":"","remark":"全球3+X进行合同的特殊需求评估与解析。","detailPicUrl":null,"logoUrl":"https://tos.trinasolar.com/n5r00e239du8tvtk1hw5o4/a4c8f566-8cdf-4761-8568-5e50c8fc37b4.jpeg","titleType":null,"imgTitle":null,"title":null,"subTitle":null,"bgImage":null,"showType":null,"alignment":null,"appStyle":null,"appType":null,"isOuter":0,"expireTime":null,"urlType":1,"configItems":null,"homePage":null,"showHomepage":0,"manageUrl":null,"isMobile":0,"isManagePcAppMenu":0,"contraction":0,"platformEnName":null,"platformCnName":null,"ieSupported":0,"ieTip":null,"isControlFP":0,"isControlDP":0,"isContainerApp":0,"tenantShow":1,"businessDomain":"ltc","tenantGroup":null,"isChangeTenant":0,"appSysType":"1171262252406378535","appGroup":"7","appServiceLevel":"1","constructionType":"0","devLanguage":"1171262252406378573,1171262252406378576","teamName":"ltc","gitlab":true,"devops":true,"apm":false,"code":"de569c25-5c8e-4fcf-ae68-954dadcc28f2","accessUrl":"scr.trinasolar.com","manualUrl":"scr.trinasolar.com","actuallyTime":null,"cluster":null,"relationSystem":null,"nodeSpec":null,"version":"V1","appDomain":null,"appName":null,"productLine":null,"bizUnit":"0","bizScopeType":"0","bizScope":"0","syncNextSystem":null,"externalProductName":null,"productVendor":null,"implVendor":null,"licenseMode":null,"licenseDescription":null,"priorityLevel":"0","serviceHours":"","description":null,"flowDomain":"","mobile":false}
2025-09-04 16:46:01.018 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | filteredBusinessData={}
2025-09-04 16:46:01.018 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=de569c25-5c8e-4fcf-ae68-954dadcc28f2
2025-09-04 16:46:01.018 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | rawBusinessData={"id":1747463606919,"createdTime":1747463606000,"creatorId":1,"creatorName":"admin","updatedTime":1754966977000,"updaterId":1,"updaterName":"admin","tenantId":1,"cnName":"供应链金融平台 SCF","cnSimpleName":"供应链金融平台 SCF","enName":"scf","enSimpleName":"scf","cloudName":null,"cloudAppId":null,"cloudProjectId":null,"cloudProjectName":null,"cloudProjectAliasName":null,"cloudTenantId":null,"cloudTenantName":null,"cloudTenantAliasName":null,"cloudNamespaceId":null,"cloudNamespaceName":null,"cloudClusterId":null,"stageStatus":"Constructing","lockFlag":"0","appUrl":"scf.trinasolar.com","remark":"供应链金融系统是一个基于集团供应链业务，为上下游企业提供融资、支付、结算等金融服务的数字化平台。","detailPicUrl":null,"logoUrl":"https://tos.trinasolar.com/n5r00e239du8tvtk1hw5o4/a4c8f566-8cdf-4761-8568-5e50c8fc37b4.jpeg","titleType":null,"imgTitle":null,"title":null,"subTitle":null,"bgImage":null,"showType":null,"alignment":null,"appStyle":null,"appType":null,"isOuter":0,"expireTime":null,"urlType":1,"configItems":null,"homePage":null,"showHomepage":0,"manageUrl":null,"isMobile":0,"isManagePcAppMenu":0,"contraction":0,"platformEnName":null,"platformCnName":null,"ieSupported":0,"ieTip":null,"isControlFP":0,"isControlDP":0,"isContainerApp":0,"tenantShow":1,"businessDomain":"mfs","tenantGroup":null,"isChangeTenant":0,"appSysType":"1171262252406378535","appGroup":"7","appServiceLevel":"1","constructionType":"2","devLanguage":"1171262252406378573,1171262252406378576","teamName":"mfs","gitlab":true,"devops":true,"apm":false,"code":"de569c25-5c8e-4fcf-ae68-954dadcc28f2","accessUrl":"scf.trinasolar.com","manualUrl":"scf.trinasolar.com","actuallyTime":null,"cluster":null,"relationSystem":null,"nodeSpec":null,"version":"V1","appDomain":null,"appName":null,"productLine":null,"bizUnit":"0","bizScopeType":"0","bizScope":"0","syncNextSystem":null,"externalProductName":null,"productVendor":null,"implVendor":null,"licenseMode":null,"licenseDescription":null,"priorityLevel":"0","serviceHours":"","description":null,"flowDomain":"","mobile":false}
2025-09-04 16:46:01.019 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | filteredBusinessData={}
2025-09-04 16:46:01.019 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=de569c25-5c8e-4fcf-ae68-954dadcc28f2
2025-09-04 16:46:01.019 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | rawBusinessData={"id":1747463606921,"createdTime":1747463606000,"creatorId":1,"creatorName":"admin","updatedTime":1754966977000,"updaterId":1,"updaterName":"admin","tenantId":1,"cnName":"HR数字化一体化平台","cnSimpleName":"HR数字化一体化平台","enName":"mhr","enSimpleName":"mhr","cloudName":null,"cloudAppId":null,"cloudProjectId":null,"cloudProjectName":null,"cloudProjectAliasName":null,"cloudTenantId":null,"cloudTenantName":null,"cloudTenantAliasName":null,"cloudNamespaceId":null,"cloudNamespaceName":null,"cloudClusterId":null,"stageStatus":"Constructing","lockFlag":"0","appUrl":"mhrportal.trinasolar.com,hrportal.trinasolar.com,minfocollect.trinasolar.com,https://infocollect.trinasolar.com,dhr.trinasolar.com","remark":"入、转、调、离、增、合同流程管理平台 【上生产更新业务负责人-江飞】","detailPicUrl":null,"logoUrl":"https://tos.trinasolar.com/n5r00e239du8tvtk1hw5o4/a4c8f566-8cdf-4761-8568-5e50c8fc37b4.jpeg","titleType":null,"imgTitle":null,"title":null,"subTitle":null,"bgImage":null,"showType":null,"alignment":null,"appStyle":null,"appType":null,"isOuter":0,"expireTime":null,"urlType":1,"configItems":null,"homePage":null,"showHomepage":0,"manageUrl":null,"isMobile":0,"isManagePcAppMenu":0,"contraction":0,"platformEnName":null,"platformCnName":null,"ieSupported":0,"ieTip":null,"isControlFP":0,"isControlDP":0,"isContainerApp":0,"tenantShow":1,"businessDomain":"mhr","tenantGroup":null,"isChangeTenant":0,"appSysType":"1171262252406378535,1171262252406378536","appGroup":"7","appServiceLevel":"2","constructionType":"0","devLanguage":"1171262252406378573,1171262252406378576","teamName":"mhr","gitlab":true,"devops":true,"apm":false,"code":"de569c25-5c8e-4fcf-ae68-954dadcc28f2","accessUrl":"mhrportal.trinasolar.com,hrportal.trinasolar.com,minfocollect.trinasolar.com,https://infocollect.trinasolar.com,dhr.trinasolar.com","manualUrl":"https://hrssc.trinasolar.com/","actuallyTime":null,"cluster":null,"relationSystem":null,"nodeSpec":null,"version":"V1","appDomain":null,"appName":null,"productLine":null,"bizUnit":"0","bizScopeType":"0","bizScope":"0","syncNextSystem":null,"externalProductName":null,"productVendor":null,"implVendor":null,"licenseMode":null,"licenseDescription":null,"priorityLevel":"0","serviceHours":"","description":null,"flowDomain":"","mobile":false}
2025-09-04 16:46:01.020 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | filteredBusinessData={}
2025-09-04 16:46:01.020 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=de569c25-5c8e-4fcf-ae68-954dadcc28f2
2025-09-04 16:46:01.020 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | rawBusinessData={"id":1747463606923,"createdTime":1747463606000,"creatorId":1,"creatorName":"admin","updatedTime":1754966977000,"updaterId":1,"updaterName":"admin","tenantId":1,"cnName":"数字化采购","cnSimpleName":"数字化采购平台(SRM）","enName":"srm","enSimpleName":"srm","cloudName":null,"cloudAppId":null,"cloudProjectId":null,"cloudProjectName":null,"cloudProjectAliasName":null,"cloudTenantId":null,"cloudTenantName":null,"cloudTenantAliasName":null,"cloudNamespaceId":null,"cloudNamespaceName":null,"cloudClusterId":null,"stageStatus":"Constructing","lockFlag":"0","appUrl":"supplier.trinasolar.com","remark":"","detailPicUrl":null,"logoUrl":"https://tos.trinasolar.com/n5r00e239du8tvtk1hw5o4/a4c8f566-8cdf-4761-8568-5e50c8fc37b4.jpeg","titleType":null,"imgTitle":null,"title":null,"subTitle":null,"bgImage":null,"showType":null,"alignment":null,"appStyle":null,"appType":null,"isOuter":0,"expireTime":null,"urlType":1,"configItems":null,"homePage":null,"showHomepage":0,"manageUrl":null,"isMobile":0,"isManagePcAppMenu":0,"contraction":0,"platformEnName":null,"platformCnName":null,"ieSupported":0,"ieTip":null,"isControlFP":0,"isControlDP":0,"isContainerApp":0,"tenantShow":1,"businessDomain":"isc","tenantGroup":null,"isChangeTenant":0,"appSysType":"1171262252406378535","appGroup":"7","appServiceLevel":"2","constructionType":"0","devLanguage":"1171262252406378573,1171262252406378576","teamName":"isc","gitlab":true,"devops":true,"apm":false,"code":"de569c25-5c8e-4fcf-ae68-954dadcc28f2","accessUrl":"supplier.trinasolar.com","manualUrl":"scm.trinasolar.com","actuallyTime":null,"cluster":null,"relationSystem":null,"nodeSpec":null,"version":"V1","appDomain":null,"appName":null,"productLine":null,"bizUnit":"0","bizScopeType":"0","bizScope":"0","syncNextSystem":null,"externalProductName":null,"productVendor":null,"implVendor":null,"licenseMode":null,"licenseDescription":null,"priorityLevel":"0","serviceHours":"","description":null,"flowDomain":"","mobile":false}
2025-09-04 16:46:01.020 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | filteredBusinessData={}
2025-09-04 16:46:01.020 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=de569c25-5c8e-4fcf-ae68-954dadcc28f2
2025-09-04 16:46:01.021 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | rawBusinessData={"id":1747463606925,"createdTime":1747463606000,"creatorId":1,"creatorName":"admin","updatedTime":1754966977000,"updaterId":1,"updaterName":"admin","tenantId":1,"cnName":"工会数字化平台","cnSimpleName":"天合工会数字化平台","enName":"union-welfare","enSimpleName":"union-welfare","cloudName":null,"cloudAppId":null,"cloudProjectId":null,"cloudProjectName":null,"cloudProjectAliasName":null,"cloudTenantId":null,"cloudTenantName":null,"cloudTenantAliasName":null,"cloudNamespaceId":null,"cloudNamespaceName":null,"cloudClusterId":null,"stageStatus":"Constructing","lockFlag":"0","appUrl":"","remark":"","detailPicUrl":null,"logoUrl":"https://tos.trinasolar.com/n5r00e239du8tvtk1hw5o4/a4c8f566-8cdf-4761-8568-5e50c8fc37b4.jpeg","titleType":null,"imgTitle":null,"title":null,"subTitle":null,"bgImage":null,"showType":null,"alignment":null,"appStyle":null,"appType":null,"isOuter":0,"expireTime":null,"urlType":1,"configItems":null,"homePage":null,"showHomepage":0,"manageUrl":null,"isMobile":0,"isManagePcAppMenu":0,"contraction":0,"platformEnName":null,"platformCnName":null,"ieSupported":0,"ieTip":null,"isControlFP":0,"isControlDP":0,"isContainerApp":0,"tenantShow":1,"businessDomain":"other","tenantGroup":null,"isChangeTenant":0,"appSysType":"1171262252406378535","appGroup":"7","appServiceLevel":"2","constructionType":"0","devLanguage":"1171262252406378573,1171262252406378576","teamName":"other","gitlab":true,"devops":true,"apm":false,"code":"de569c25-5c8e-4fcf-ae68-954dadcc28f2","accessUrl":"","manualUrl":"","actuallyTime":null,"cluster":null,"relationSystem":null,"nodeSpec":null,"version":"V1","appDomain":null,"appName":null,"productLine":null,"bizUnit":"0","bizScopeType":"0","bizScope":"0","syncNextSystem":null,"externalProductName":null,"productVendor":null,"implVendor":null,"licenseMode":null,"licenseDescription":null,"priorityLevel":"0","serviceHours":"","description":null,"flowDomain":"","mobile":false}
2025-09-04 16:46:01.021 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | filteredBusinessData={}
2025-09-04 16:46:01.021 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=de569c25-5c8e-4fcf-ae68-954dadcc28f2
2025-09-04 16:46:01.021 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | rawBusinessData={"id":1747463606926,"createdTime":1747463606000,"creatorId":1,"creatorName":"admin","updatedTime":1754966977000,"updaterId":1,"updaterName":"admin","tenantId":1,"cnName":"一站式接待服务平台","cnSimpleName":"一站式接待服务平台","enName":"entertain","enSimpleName":"entertain","cloudName":null,"cloudAppId":null,"cloudProjectId":null,"cloudProjectName":null,"cloudProjectAliasName":null,"cloudTenantId":null,"cloudTenantName":null,"cloudTenantAliasName":null,"cloudNamespaceId":null,"cloudNamespaceName":null,"cloudClusterId":null,"stageStatus":"Constructing","lockFlag":"0","appUrl":"rsp.trinasolar.com","remark":"一站式接待服务平台：核心处理接待发起、用车服务、参观服务、住宿服务、用餐服务、会议服务等","detailPicUrl":null,"logoUrl":"https://tos.trinasolar.com/n5r00e239du8tvtk1hw5o4/a4c8f566-8cdf-4761-8568-5e50c8fc37b4.jpeg","titleType":null,"imgTitle":null,"title":null,"subTitle":null,"bgImage":null,"showType":null,"alignment":null,"appStyle":null,"appType":null,"isOuter":0,"expireTime":null,"urlType":1,"configItems":null,"homePage":null,"showHomepage":0,"manageUrl":null,"isMobile":0,"isManagePcAppMenu":0,"contraction":0,"platformEnName":null,"platformCnName":null,"ieSupported":0,"ieTip":null,"isControlFP":0,"isControlDP":0,"isContainerApp":0,"tenantShow":1,"businessDomain":"other","tenantGroup":null,"isChangeTenant":0,"appSysType":"1171262252406378535,1171262252406378536","appGroup":"7","appServiceLevel":"2","constructionType":"0","devLanguage":"1171262252406378573,1171262252406378576","teamName":"other","gitlab":true,"devops":true,"apm":false,"code":"de569c25-5c8e-4fcf-ae68-954dadcc28f2","accessUrl":"rsp.trinasolar.com","manualUrl":"rsp.trinasolar.com","actuallyTime":null,"cluster":null,"relationSystem":null,"nodeSpec":null,"version":"V1","appDomain":null,"appName":null,"productLine":null,"bizUnit":"0","bizScopeType":"0","bizScope":"0","syncNextSystem":null,"externalProductName":null,"productVendor":null,"implVendor":null,"licenseMode":null,"licenseDescription":null,"priorityLevel":"0","serviceHours":"","description":null,"flowDomain":"","mobile":false}
2025-09-04 16:46:01.021 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | filteredBusinessData={}
2025-09-04 16:46:01.021 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=de569c25-5c8e-4fcf-ae68-954dadcc28f2
2025-09-04 16:46:01.022 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | rawBusinessData={"id":1747463606928,"createdTime":1747463606000,"creatorId":1,"creatorName":"admin","updatedTime":1754966977000,"updaterId":1,"updaterName":"admin","tenantId":1,"cnName":"信用系统","cnSimpleName":"信用系统","enName":"trust","enSimpleName":"trust","cloudName":null,"cloudAppId":null,"cloudProjectId":null,"cloudProjectName":null,"cloudProjectAliasName":null,"cloudTenantId":null,"cloudTenantName":null,"cloudTenantAliasName":null,"cloudNamespaceId":null,"cloudNamespaceName":null,"cloudClusterId":null,"stageStatus":"Constructing","lockFlag":"0","appUrl":"https://credit.trinasolar.com/","remark":"天合客户赊销管理、出运申报管理【业务负责人-马硕】","detailPicUrl":null,"logoUrl":"https://tos.trinasolar.com/n5r00e239du8tvtk1hw5o4/a4c8f566-8cdf-4761-8568-5e50c8fc37b4.jpeg","titleType":null,"imgTitle":null,"title":null,"subTitle":null,"bgImage":null,"showType":null,"alignment":null,"appStyle":null,"appType":null,"isOuter":0,"expireTime":null,"urlType":1,"configItems":null,"homePage":null,"showHomepage":0,"manageUrl":null,"isMobile":0,"isManagePcAppMenu":0,"contraction":0,"platformEnName":null,"platformCnName":null,"ieSupported":0,"ieTip":null,"isControlFP":0,"isControlDP":0,"isContainerApp":0,"tenantShow":1,"businessDomain":"mfs","tenantGroup":null,"isChangeTenant":0,"appSysType":"1171262252406378535,1171262252406378536","appGroup":"7","appServiceLevel":"2","constructionType":"0","devLanguage":"1171262252406378573,1171262252406378576","teamName":"mfs","gitlab":true,"devops":true,"apm":false,"code":"de569c25-5c8e-4fcf-ae68-954dadcc28f2","accessUrl":"https://credit.trinasolar.com/","manualUrl":"https://credit.trinasolar.com/","actuallyTime":null,"cluster":null,"relationSystem":null,"nodeSpec":null,"version":"V1","appDomain":null,"appName":null,"productLine":null,"bizUnit":"0","bizScopeType":"0","bizScope":"0","syncNextSystem":null,"externalProductName":null,"productVendor":null,"implVendor":null,"licenseMode":null,"licenseDescription":null,"priorityLevel":"0","serviceHours":"","description":null,"flowDomain":"","mobile":false}
2025-09-04 16:46:01.022 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | filteredBusinessData={}
2025-09-04 16:46:01.022 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=de569c25-5c8e-4fcf-ae68-954dadcc28f2
2025-09-04 16:46:01.023 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | rawBusinessData={"id":1747463606930,"createdTime":1747463606000,"creatorId":1,"creatorName":"admin","updatedTime":1754966977000,"updaterId":1,"updaterName":"admin","tenantId":1,"cnName":"档案管理系统","cnSimpleName":"档案管理系统","enName":"devops-document ","enSimpleName":"devops-document ","cloudName":null,"cloudAppId":null,"cloudProjectId":null,"cloudProjectName":null,"cloudProjectAliasName":null,"cloudTenantId":null,"cloudTenantName":null,"cloudTenantAliasName":null,"cloudNamespaceId":null,"cloudNamespaceName":null,"cloudClusterId":null,"stageStatus":"Constructing","lockFlag":"0","appUrl":"https://archive.trinasolar.com/","remark":"合同、档案管理 【业务负责人-葛秋珍】","detailPicUrl":null,"logoUrl":"https://tos.trinasolar.com/n5r00e239du8tvtk1hw5o4/a4c8f566-8cdf-4761-8568-5e50c8fc37b4.jpeg","titleType":null,"imgTitle":null,"title":null,"subTitle":null,"bgImage":null,"showType":null,"alignment":null,"appStyle":null,"appType":null,"isOuter":0,"expireTime":null,"urlType":1,"configItems":null,"homePage":null,"showHomepage":0,"manageUrl":null,"isMobile":0,"isManagePcAppMenu":0,"contraction":0,"platformEnName":null,"platformCnName":null,"ieSupported":0,"ieTip":null,"isControlFP":0,"isControlDP":0,"isContainerApp":0,"tenantShow":1,"businessDomain":"other","tenantGroup":null,"isChangeTenant":0,"appSysType":"1171262252406378535","appGroup":"7","appServiceLevel":"2","constructionType":"0","devLanguage":"1171262252406378573,1171262252406378576","teamName":"other","gitlab":true,"devops":true,"apm":false,"code":"de569c25-5c8e-4fcf-ae68-954dadcc28f2","accessUrl":"https://archive.trinasolar.com/","manualUrl":"https://archive.trinasolar.com/","actuallyTime":null,"cluster":null,"relationSystem":null,"nodeSpec":null,"version":"V1","appDomain":null,"appName":null,"productLine":null,"bizUnit":"0","bizScopeType":"0","bizScope":"0","syncNextSystem":null,"externalProductName":null,"productVendor":null,"implVendor":null,"licenseMode":null,"licenseDescription":null,"priorityLevel":"0","serviceHours":"","description":null,"flowDomain":"","mobile":false}
2025-09-04 16:46:01.024 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | filteredBusinessData={}
2025-09-04 16:46:01.024 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=de569c25-5c8e-4fcf-ae68-954dadcc28f2
2025-09-04 16:46:01.024 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | rawBusinessData={"id":1747463606932,"createdTime":1747463606000,"creatorId":1,"creatorName":"admin","updatedTime":1754966977000,"updaterId":1,"updaterName":"admin","tenantId":1,"cnName":"组件溯源","cnSimpleName":"组件溯源(sn-tracking)","enName":"sn-tracking","enSimpleName":"sn-tracking","cloudName":null,"cloudAppId":null,"cloudProjectId":null,"cloudProjectName":null,"cloudProjectAliasName":null,"cloudTenantId":null,"cloudTenantName":null,"cloudTenantAliasName":null,"cloudNamespaceId":null,"cloudNamespaceName":null,"cloudClusterId":null,"stageStatus":"Constructing","lockFlag":"0","appUrl":"https://moduletracking.trinasolar.com/","remark":"组件、合同、发货信息查询平台 【业务负责人-仇叶飞】","detailPicUrl":null,"logoUrl":"https://tos.trinasolar.com/n5r00e239du8tvtk1hw5o4/a4c8f566-8cdf-4761-8568-5e50c8fc37b4.jpeg","titleType":null,"imgTitle":null,"title":null,"subTitle":null,"bgImage":null,"showType":null,"alignment":null,"appStyle":null,"appType":null,"isOuter":0,"expireTime":null,"urlType":1,"configItems":null,"homePage":null,"showHomepage":0,"manageUrl":null,"isMobile":0,"isManagePcAppMenu":0,"contraction":0,"platformEnName":null,"platformCnName":null,"ieSupported":0,"ieTip":null,"isControlFP":0,"isControlDP":0,"isContainerApp":0,"tenantShow":1,"businessDomain":"ltc","tenantGroup":null,"isChangeTenant":0,"appSysType":"1171262252406378535","appGroup":"7","appServiceLevel":"2","constructionType":"0","devLanguage":"1171262252406378573,1171262252406378576","teamName":"ltc","gitlab":true,"devops":true,"apm":false,"code":"de569c25-5c8e-4fcf-ae68-954dadcc28f2","accessUrl":"https://moduletracking.trinasolar.com/","manualUrl":"","actuallyTime":null,"cluster":null,"relationSystem":null,"nodeSpec":null,"version":"V1","appDomain":null,"appName":null,"productLine":null,"bizUnit":"0","bizScopeType":"0","bizScope":"0","syncNextSystem":null,"externalProductName":null,"productVendor":null,"implVendor":null,"licenseMode":null,"licenseDescription":null,"priorityLevel":"0","serviceHours":"","description":null,"flowDomain":"","mobile":false}
2025-09-04 16:46:01.024 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | filteredBusinessData={}
2025-09-04 16:46:01.024 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=de569c25-5c8e-4fcf-ae68-954dadcc28f2
2025-09-04 16:46:01.025 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | rawBusinessData={"id":1747463606934,"createdTime":1747463606000,"creatorId":1,"creatorName":"admin","updatedTime":1754966977000,"updaterId":1,"updaterName":"admin","tenantId":1,"cnName":"服务交付-项目管理系统","cnSimpleName":"服务交付-项目管理系统","enName":"pms","enSimpleName":"pms","cloudName":null,"cloudAppId":null,"cloudProjectId":null,"cloudProjectName":null,"cloudProjectAliasName":null,"cloudTenantId":null,"cloudTenantName":null,"cloudTenantAliasName":null,"cloudNamespaceId":null,"cloudNamespaceName":null,"cloudClusterId":null,"stageStatus":"Constructing","lockFlag":"0","appUrl":"https://sd-pms.trinasolar.com/","remark":"用于支架业务的项目管理","detailPicUrl":null,"logoUrl":"https://tos.trinasolar.com/n5r00e239du8tvtk1hw5o4/a4c8f566-8cdf-4761-8568-5e50c8fc37b4.jpeg","titleType":null,"imgTitle":null,"title":null,"subTitle":null,"bgImage":null,"showType":null,"alignment":null,"appStyle":null,"appType":null,"isOuter":0,"expireTime":null,"urlType":1,"configItems":null,"homePage":null,"showHomepage":0,"manageUrl":null,"isMobile":0,"isManagePcAppMenu":0,"contraction":0,"platformEnName":null,"platformCnName":null,"ieSupported":0,"ieTip":null,"isControlFP":0,"isControlDP":0,"isContainerApp":0,"tenantShow":1,"businessDomain":"ipd","tenantGroup":null,"isChangeTenant":0,"appSysType":"1171262252406378535","appGroup":"7","appServiceLevel":"2","constructionType":"2","devLanguage":"1171262252406378573,1171262252406378576","teamName":"ipd","gitlab":true,"devops":true,"apm":false,"code":"de569c25-5c8e-4fcf-ae68-954dadcc28f2","accessUrl":"https://sd-pms.trinasolar.com/","manualUrl":"","actuallyTime":null,"cluster":null,"relationSystem":null,"nodeSpec":null,"version":"V1","appDomain":null,"appName":null,"productLine":null,"bizUnit":"0","bizScopeType":"0","bizScope":"0","syncNextSystem":null,"externalProductName":null,"productVendor":null,"implVendor":null,"licenseMode":null,"licenseDescription":null,"priorityLevel":"0","serviceHours":"","description":null,"flowDomain":"","mobile":false}
2025-09-04 16:46:01.025 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | filteredBusinessData={}
2025-09-04 16:46:01.025 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=de569c25-5c8e-4fcf-ae68-954dadcc28f2
2025-09-04 16:46:01.035 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | rawBusinessData={"id":1747463606936,"createdTime":1747463606000,"creatorId":1,"creatorName":"admin","updatedTime":1754966977000,"updaterId":1,"updaterName":"admin","tenantId":1,"cnName":"智慧税务平台","cnSimpleName":"智慧税务平台 GTDP","enName":"gtdp","enSimpleName":"gtdp","cloudName":null,"cloudAppId":null,"cloudProjectId":null,"cloudProjectName":null,"cloudProjectAliasName":null,"cloudTenantId":null,"cloudTenantName":null,"cloudTenantAliasName":null,"cloudNamespaceId":null,"cloudNamespaceName":null,"cloudClusterId":null,"stageStatus":"Constructing","lockFlag":"0","appUrl":"gtdp.trinasolar.com","remark":"智慧税务平台核心功能是处理发票管理、税种申报、税种提计、非贸付汇、定价转让等","detailPicUrl":null,"logoUrl":"https://tos.trinasolar.com/n5r00e239du8tvtk1hw5o4/a4c8f566-8cdf-4761-8568-5e50c8fc37b4.jpeg","titleType":null,"imgTitle":null,"title":null,"subTitle":null,"bgImage":null,"showType":null,"alignment":null,"appStyle":null,"appType":null,"isOuter":0,"expireTime":null,"urlType":1,"configItems":null,"homePage":null,"showHomepage":0,"manageUrl":null,"isMobile":0,"isManagePcAppMenu":0,"contraction":0,"platformEnName":null,"platformCnName":null,"ieSupported":0,"ieTip":null,"isControlFP":0,"isControlDP":0,"isContainerApp":0,"tenantShow":1,"businessDomain":"mfs","tenantGroup":null,"isChangeTenant":0,"appSysType":"1171262252406378535,1171262252406378536","appGroup":"7","appServiceLevel":"2","constructionType":"2","devLanguage":"1171262252406378573,1171262252406378576","teamName":"mfs","gitlab":true,"devops":true,"apm":false,"code":"de569c25-5c8e-4fcf-ae68-954dadcc28f2","accessUrl":"gtdp.trinasolar.com","manualUrl":"gtdp.trinasolar.com","actuallyTime":null,"cluster":null,"relationSystem":null,"nodeSpec":null,"version":"V1","appDomain":null,"appName":null,"productLine":null,"bizUnit":"0","bizScopeType":"0","bizScope":"0","syncNextSystem":null,"externalProductName":null,"productVendor":null,"implVendor":null,"licenseMode":null,"licenseDescription":null,"priorityLevel":"0","serviceHours":"","description":null,"flowDomain":"","mobile":false}
2025-09-04 16:46:01.035 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | filteredBusinessData={}
2025-09-04 16:46:01.035 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=de569c25-5c8e-4fcf-ae68-954dadcc28f2
2025-09-04 16:46:01.035 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | rawBusinessData={"id":1747463606938,"createdTime":1747463606000,"creatorId":1,"creatorName":"admin","updatedTime":1754966977000,"updaterId":1,"updaterName":"admin","tenantId":1,"cnName":"物资出门","cnSimpleName":"物资出门项目","enName":"outdoor","enSimpleName":"outdoor","cloudName":null,"cloudAppId":null,"cloudProjectId":null,"cloudProjectName":null,"cloudProjectAliasName":null,"cloudTenantId":null,"cloudTenantName":null,"cloudTenantAliasName":null,"cloudNamespaceId":null,"cloudNamespaceName":null,"cloudClusterId":null,"stageStatus":"Constructing","lockFlag":"0","appUrl":"https://outdoor.trinasolar.com/","remark":"物资出门跟踪、安检管理平台","detailPicUrl":null,"logoUrl":"https://tos.trinasolar.com/n5r00e239du8tvtk1hw5o4/a4c8f566-8cdf-4761-8568-5e50c8fc37b4.jpeg","titleType":null,"imgTitle":null,"title":null,"subTitle":null,"bgImage":null,"showType":null,"alignment":null,"appStyle":null,"appType":null,"isOuter":0,"expireTime":null,"urlType":1,"configItems":null,"homePage":null,"showHomepage":0,"manageUrl":null,"isMobile":0,"isManagePcAppMenu":0,"contraction":0,"platformEnName":null,"platformCnName":null,"ieSupported":0,"ieTip":null,"isControlFP":0,"isControlDP":0,"isContainerApp":0,"tenantShow":1,"businessDomain":"other","tenantGroup":null,"isChangeTenant":0,"appSysType":"1171262252406378535,1171262252406378536","appGroup":"7","appServiceLevel":"2","constructionType":"0","devLanguage":"1171262252406378573,1171262252406378576","teamName":"other","gitlab":true,"devops":true,"apm":false,"code":"de569c25-5c8e-4fcf-ae68-954dadcc28f2","accessUrl":"https://outdoor.trinasolar.com/","manualUrl":"https://outdoor.trinasolar.com/","actuallyTime":null,"cluster":null,"relationSystem":null,"nodeSpec":null,"version":"V1","appDomain":null,"appName":null,"productLine":null,"bizUnit":"0","bizScopeType":"0","bizScope":"0","syncNextSystem":null,"externalProductName":null,"productVendor":null,"implVendor":null,"licenseMode":null,"licenseDescription":null,"priorityLevel":"0","serviceHours":"","description":null,"flowDomain":"","mobile":false}
2025-09-04 16:46:01.036 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | filteredBusinessData={}
2025-09-04 16:46:01.036 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=de569c25-5c8e-4fcf-ae68-954dadcc28f2
2025-09-04 16:46:01.036 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | rawBusinessData={"id":1747463606941,"createdTime":1751262491000,"creatorId":1,"creatorName":"admin","updatedTime":1754966977000,"updaterId":1,"updaterName":"admin","tenantId":1,"cnName":"户储云平台","cnSimpleName":"户储云平台","enName":"trinastorge","enSimpleName":"trinastorge","cloudName":null,"cloudAppId":null,"cloudProjectId":null,"cloudProjectName":null,"cloudProjectAliasName":null,"cloudTenantId":null,"cloudTenantName":null,"cloudTenantAliasName":null,"cloudNamespaceId":null,"cloudNamespaceName":null,"cloudClusterId":null,"stageStatus":"Constructing","lockFlag":"0","appUrl":"","remark":"为构建【天合光储云管理平台】,实现对天合户用储能产品及海外户用电站资产的采集、监控等，打造“智能监控、智能运维”的户用模式。天合-远景项目组将联合打造： 1. 基于EnOS HEMS产品定制开发Trina户储管理系统；2.部署EnOS平台及光储HEMS系统；3.实现与选型采集棒的系统集成对接；","detailPicUrl":null,"logoUrl":"https://tos.trinasolar.com/n5r00e239du8tvtk1hw5o4/a4c8f566-8cdf-4761-8568-5e50c8fc37b4.jpeg","titleType":null,"imgTitle":null,"title":null,"subTitle":null,"bgImage":null,"showType":null,"alignment":null,"appStyle":null,"appType":null,"isOuter":0,"expireTime":null,"urlType":1,"configItems":null,"homePage":null,"showHomepage":0,"manageUrl":null,"isMobile":0,"isManagePcAppMenu":0,"contraction":0,"platformEnName":null,"platformCnName":null,"ieSupported":0,"ieTip":null,"isControlFP":0,"isControlDP":0,"isContainerApp":0,"tenantShow":1,"businessDomain":"ipd","tenantGroup":null,"isChangeTenant":0,"appSysType":"1171262252406378535,1171262252406378534","appGroup":"7","appServiceLevel":"3","constructionType":"0","devLanguage":"1171262252406378573,1171262252406378576","teamName":"ipd","gitlab":true,"devops":true,"apm":false,"code":"de569c25-5c8e-4fcf-ae68-954dadcc28f2","accessUrl":"https://app-portal-storage.trinasolar.com/hossain-fe/index.html","manualUrl":"","actuallyTime":null,"cluster":null,"relationSystem":null,"nodeSpec":null,"version":"V1","appDomain":null,"appName":null,"productLine":null,"bizUnit":"0","bizScopeType":"0","bizScope":"0","syncNextSystem":null,"externalProductName":null,"productVendor":null,"implVendor":null,"licenseMode":null,"licenseDescription":null,"priorityLevel":"0","serviceHours":"","description":null,"flowDomain":"","mobile":false}
2025-09-04 16:46:01.036 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | filteredBusinessData={}
2025-09-04 16:46:01.036 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=de569c25-5c8e-4fcf-ae68-954dadcc28f2
2025-09-04 16:46:01.037 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | rawBusinessData={"id":1747463606942,"createdTime":1751262491000,"creatorId":1,"creatorName":"admin","updatedTime":1754966977000,"updaterId":1,"updaterName":"admin","tenantId":1,"cnName":"组织碳管理","cnSimpleName":"组织碳","enName":"carbon-boot","enSimpleName":"carbon-boot","cloudName":null,"cloudAppId":null,"cloudProjectId":null,"cloudProjectName":null,"cloudProjectAliasName":null,"cloudTenantId":null,"cloudTenantName":null,"cloudTenantAliasName":null,"cloudNamespaceId":null,"cloudNamespaceName":null,"cloudClusterId":null,"stageStatus":"Constructing","lockFlag":"0","appUrl":"","remark":"1.搭建完善的数据采集体系，助力基地零碳工厂评选；2.尽早布局应对多变的国内、国外碳政策形势的信息化产品；3.搭建界面美观、数据丰富的可视化大屏.","detailPicUrl":null,"logoUrl":"https://tos.trinasolar.com/n5r00e239du8tvtk1hw5o4/a4c8f566-8cdf-4761-8568-5e50c8fc37b4.jpeg","titleType":null,"imgTitle":null,"title":null,"subTitle":null,"bgImage":null,"showType":null,"alignment":null,"appStyle":null,"appType":null,"isOuter":0,"expireTime":null,"urlType":1,"configItems":null,"homePage":null,"showHomepage":0,"manageUrl":null,"isMobile":0,"isManagePcAppMenu":0,"contraction":0,"platformEnName":null,"platformCnName":null,"ieSupported":0,"ieTip":null,"isControlFP":0,"isControlDP":0,"isContainerApp":0,"tenantShow":1,"businessDomain":"other","tenantGroup":null,"isChangeTenant":0,"appSysType":"1171262252406378535","appGroup":"7","appServiceLevel":"3","constructionType":"0","devLanguage":"1171262252406378573,1171262252406378576","teamName":"other","gitlab":true,"devops":true,"apm":false,"code":"de569c25-5c8e-4fcf-ae68-954dadcc28f2","accessUrl":"http://*************","manualUrl":"","actuallyTime":null,"cluster":null,"relationSystem":null,"nodeSpec":null,"version":"V1","appDomain":null,"appName":null,"productLine":null,"bizUnit":"0","bizScopeType":"0","bizScope":"0","syncNextSystem":null,"externalProductName":null,"productVendor":null,"implVendor":null,"licenseMode":null,"licenseDescription":null,"priorityLevel":"0","serviceHours":"","description":null,"flowDomain":"","mobile":false}
2025-09-04 16:46:01.037 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | filteredBusinessData={}
2025-09-04 16:46:01.037 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=de569c25-5c8e-4fcf-ae68-954dadcc28f2
2025-09-04 16:46:01.037 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | rawBusinessData={"id":1747463606943,"createdTime":1751262491000,"creatorId":1,"creatorName":"admin","updatedTime":1754966977000,"updaterId":1,"updaterName":"admin","tenantId":1,"cnName":"EHS数字化平台","cnSimpleName":"EHS系统","enName":"ehsdmp","enSimpleName":"ehsdmp","cloudName":null,"cloudAppId":null,"cloudProjectId":null,"cloudProjectName":null,"cloudProjectAliasName":null,"cloudTenantId":null,"cloudTenantName":null,"cloudTenantAliasName":null,"cloudNamespaceId":null,"cloudNamespaceName":null,"cloudClusterId":null,"stageStatus":"Constructing","lockFlag":"0","appUrl":"","remark":"打造数字化的EHS管理平台","detailPicUrl":null,"logoUrl":"https://tos.trinasolar.com/n5r00e239du8tvtk1hw5o4/a4c8f566-8cdf-4761-8568-5e50c8fc37b4.jpeg","titleType":null,"imgTitle":null,"title":null,"subTitle":null,"bgImage":null,"showType":null,"alignment":null,"appStyle":null,"appType":null,"isOuter":0,"expireTime":null,"urlType":1,"configItems":null,"homePage":null,"showHomepage":0,"manageUrl":null,"isMobile":0,"isManagePcAppMenu":0,"contraction":0,"platformEnName":null,"platformCnName":null,"ieSupported":0,"ieTip":null,"isControlFP":0,"isControlDP":0,"isContainerApp":0,"tenantShow":1,"businessDomain":"other","tenantGroup":null,"isChangeTenant":0,"appSysType":"1171262252406378535,1171262252406378536","appGroup":"7","appServiceLevel":"3","constructionType":"0","devLanguage":"1171262252406378573,1171262252406378576","teamName":"other","gitlab":true,"devops":true,"apm":false,"code":"de569c25-5c8e-4fcf-ae68-954dadcc28f2","accessUrl":"https://ehsdmp.trinasolar.com,https://ehsdmp.trinasolar.com/admin/#/","manualUrl":"","actuallyTime":null,"cluster":null,"relationSystem":null,"nodeSpec":null,"version":"V1","appDomain":null,"appName":null,"productLine":null,"bizUnit":"0","bizScopeType":"0","bizScope":"0","syncNextSystem":null,"externalProductName":null,"productVendor":null,"implVendor":null,"licenseMode":null,"licenseDescription":null,"priorityLevel":"0","serviceHours":"","description":null,"flowDomain":"","mobile":false}
2025-09-04 16:46:01.037 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | filteredBusinessData={}
2025-09-04 16:46:01.037 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=de569c25-5c8e-4fcf-ae68-954dadcc28f2
2025-09-04 16:46:01.038 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | rawBusinessData={"id":1747463606944,"createdTime":1751262491000,"creatorId":1,"creatorName":"admin","updatedTime":1754966977000,"updaterId":1,"updaterName":"admin","tenantId":1,"cnName":"产品碳系统","cnSimpleName":"产品碳足迹","enName":"cfp","enSimpleName":"cfp","cloudName":null,"cloudAppId":null,"cloudProjectId":null,"cloudProjectName":null,"cloudProjectAliasName":null,"cloudTenantId":null,"cloudTenantName":null,"cloudTenantAliasName":null,"cloudNamespaceId":null,"cloudNamespaceName":null,"cloudClusterId":null,"stageStatus":"Constructing","lockFlag":"0","appUrl":"","remark":"打造“供应商收资-产品碳数据计算-报告生成-审核发布”的产品碳管理闭环","detailPicUrl":null,"logoUrl":"https://tos.trinasolar.com/n5r00e239du8tvtk1hw5o4/a4c8f566-8cdf-4761-8568-5e50c8fc37b4.jpeg","titleType":null,"imgTitle":null,"title":null,"subTitle":null,"bgImage":null,"showType":null,"alignment":null,"appStyle":null,"appType":null,"isOuter":0,"expireTime":null,"urlType":1,"configItems":null,"homePage":null,"showHomepage":0,"manageUrl":null,"isMobile":0,"isManagePcAppMenu":0,"contraction":0,"platformEnName":null,"platformCnName":null,"ieSupported":0,"ieTip":null,"isControlFP":0,"isControlDP":0,"isContainerApp":0,"tenantShow":1,"businessDomain":"other","tenantGroup":null,"isChangeTenant":0,"appSysType":"1171262252406378535","appGroup":"7","appServiceLevel":"3","constructionType":"1","devLanguage":"1171262252406378573,1171262252406378576","teamName":"other","gitlab":true,"devops":true,"apm":false,"code":"de569c25-5c8e-4fcf-ae68-954dadcc28f2","accessUrl":"https://cfp.trinasolar.com,https://cfp-supp.trinasolar.com,https://cfp-ca.trinasolar.com","manualUrl":"","actuallyTime":null,"cluster":null,"relationSystem":null,"nodeSpec":null,"version":"V1","appDomain":null,"appName":null,"productLine":null,"bizUnit":"0","bizScopeType":"0","bizScope":"0","syncNextSystem":null,"externalProductName":null,"productVendor":null,"implVendor":null,"licenseMode":null,"licenseDescription":null,"priorityLevel":"0","serviceHours":"","description":null,"flowDomain":"","mobile":false}
2025-09-04 16:46:01.038 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | filteredBusinessData={}
2025-09-04 16:46:01.038 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=de569c25-5c8e-4fcf-ae68-954dadcc28f2
2025-09-04 16:46:01.038 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | rawBusinessData={"id":"1923581094534770690","createdTime":1747452446000,"creatorId":"1923294602840059905","creatorName":"杨刚 (Yang Gang)","updatedTime":1754966977000,"updaterId":"1923294602840059905","updaterName":"杨刚 (Yang Gang)","tenantId":1,"cnName":"订单全链路","cnSimpleName":"订单全链路","enName":"oms","enSimpleName":"oms","cloudName":null,"cloudAppId":null,"cloudProjectId":null,"cloudProjectName":null,"cloudProjectAliasName":null,"cloudTenantId":null,"cloudTenantName":null,"cloudTenantAliasName":null,"cloudNamespaceId":null,"cloudNamespaceName":null,"cloudClusterId":null,"stageStatus":"Constructing","lockFlag":"0","appUrl":null,"remark":"OMS全链路运营可视项目以订单为核心驱动业务跨领域集成，通过智能技术，实现价值流高效运营，打造订单一站式订单服务系统；通过规则优化、系统集成、数据打通，实现过程可视、周期缩短、客户满意度的提升，增强客户粘性，助力业务战略实现。","detailPicUrl":null,"logoUrl":"https://tos.trinasolar.com/0lkj34hlov1zalhxz28f62/1bc39b1d-57c7-4606-ad61-0231757321b0.jpg","titleType":null,"imgTitle":null,"title":null,"subTitle":null,"bgImage":null,"showType":null,"alignment":null,"appStyle":null,"appType":null,"isOuter":null,"expireTime":null,"urlType":null,"configItems":null,"homePage":null,"showHomepage":0,"manageUrl":null,"isMobile":0,"isManagePcAppMenu":0,"contraction":0,"platformEnName":null,"platformCnName":null,"ieSupported":0,"ieTip":"null","isControlFP":null,"isControlDP":null,"isContainerApp":null,"tenantShow":null,"businessDomain":"isc","tenantGroup":null,"isChangeTenant":null,"appSysType":"1171262252406378536,1171262252406378535","appGroup":null,"appServiceLevel":"1","constructionType":"0","devLanguage":"1171262252406378573,1171262252406378576","teamName":"isc","gitlab":true,"devops":true,"apm":true,"code":"de569c25-5c8e-4fcf-ae68-954dadcc28f2","accessUrl":null,"manualUrl":null,"actuallyTime":null,"cluster":null,"relationSystem":null,"nodeSpec":null,"version":"V1","appDomain":null,"appName":null,"productLine":null,"bizUnit":"0","bizScopeType":"0","bizScope":"0","syncNextSystem":null,"externalProductName":null,"productVendor":null,"implVendor":null,"licenseMode":null,"licenseDescription":null,"priorityLevel":"0","serviceHours":"","description":null,"flowDomain":"","mobile":false}
2025-09-04 16:46:01.038 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | filteredBusinessData={}
2025-09-04 16:46:01.038 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=de569c25-5c8e-4fcf-ae68-954dadcc28f2
2025-09-04 16:46:01.039 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | rawBusinessData={"id":"1924646217643036673","createdTime":1747706391000,"creatorId":"1923294602840059905","creatorName":"杨刚 (Yang Gang)","updatedTime":1754966977000,"updaterId":"1923294602840059905","updaterName":"杨刚 (Yang Gang)","tenantId":1,"cnName":"满意度调查","cnSimpleName":"满意度调查","enName":"myddc","enSimpleName":"myddc","cloudName":null,"cloudAppId":null,"cloudProjectId":null,"cloudProjectName":null,"cloudProjectAliasName":null,"cloudTenantId":null,"cloudTenantName":null,"cloudTenantAliasName":null,"cloudNamespaceId":null,"cloudNamespaceName":null,"cloudClusterId":null,"stageStatus":"Constructing","lockFlag":"0","appUrl":null,"remark":"满意度调查【客户使用中，别删！】","detailPicUrl":null,"logoUrl":"https://tos.trinasolar.com/0lkj34hlov1zalhxz28f62/9a615b2e-321f-4ba3-b145-738bdadbd385.png","titleType":null,"imgTitle":null,"title":null,"subTitle":null,"bgImage":null,"showType":null,"alignment":null,"appStyle":null,"appType":null,"isOuter":null,"expireTime":null,"urlType":null,"configItems":null,"homePage":null,"showHomepage":0,"manageUrl":null,"isMobile":0,"isManagePcAppMenu":0,"contraction":0,"platformEnName":null,"platformCnName":null,"ieSupported":0,"ieTip":"null","isControlFP":null,"isControlDP":null,"isContainerApp":null,"tenantShow":null,"businessDomain":"tasptest","tenantGroup":null,"isChangeTenant":null,"appSysType":"1171262252406378535","appGroup":null,"appServiceLevel":"1","constructionType":"0","devLanguage":"1171262252406378573","teamName":"tasptest","gitlab":true,"devops":true,"apm":true,"code":"f93d426a-661c-4888-a2f8-e37e5d7b568f","accessUrl":null,"manualUrl":null,"actuallyTime":null,"cluster":null,"relationSystem":null,"nodeSpec":null,"version":"V1","appDomain":null,"appName":null,"productLine":null,"bizUnit":"0","bizScopeType":"0","bizScope":"0","syncNextSystem":null,"externalProductName":null,"productVendor":null,"implVendor":null,"licenseMode":null,"licenseDescription":null,"priorityLevel":"0","serviceHours":"","description":null,"flowDomain":"","mobile":false}
2025-09-04 16:46:01.039 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | filteredBusinessData={}
2025-09-04 16:46:01.039 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=f93d426a-661c-4888-a2f8-e37e5d7b568f
2025-09-04 16:46:01.039 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | rawBusinessData={"id":"1925489080948842498","createdTime":1747907345000,"creatorId":"1923294578580205569","creatorName":"沈志祁 (Shen Zhiqi)","updatedTime":1754966977000,"updaterId":"1923294578580205569","updaterName":"沈志祁 (Shen Zhiqi)","tenantId":1,"cnName":"EHS管理系统","cnSimpleName":"EHS","enName":"ehs","enSimpleName":"ehs","cloudName":null,"cloudAppId":null,"cloudProjectId":null,"cloudProjectName":null,"cloudProjectAliasName":null,"cloudTenantId":null,"cloudTenantName":null,"cloudTenantAliasName":null,"cloudNamespaceId":null,"cloudNamespaceName":null,"cloudClusterId":null,"stageStatus":"Constructing","lockFlag":"0","appUrl":null,"remark":null,"detailPicUrl":null,"logoUrl":"https://tos.trinasolar.com/0lkj34hlov1zalhxz28f62/008477ad-8aee-42d1-9fdb-f3ea11a6dbe0.png","titleType":null,"imgTitle":null,"title":null,"subTitle":null,"bgImage":null,"showType":null,"alignment":null,"appStyle":null,"appType":null,"isOuter":null,"expireTime":null,"urlType":null,"configItems":null,"homePage":null,"showHomepage":0,"manageUrl":null,"isMobile":0,"isManagePcAppMenu":0,"contraction":0,"platformEnName":null,"platformCnName":null,"ieSupported":0,"ieTip":"null","isControlFP":null,"isControlDP":null,"isContainerApp":null,"tenantShow":null,"businessDomain":"tasptest","tenantGroup":null,"isChangeTenant":null,"appSysType":"1171262252406378535","appGroup":null,"appServiceLevel":"3","constructionType":"0","devLanguage":"1171262252406378576","teamName":"tasptest","gitlab":true,"devops":true,"apm":true,"code":"f43a5372-6e8f-42d8-9c21-0a2bd3fa47b2","accessUrl":null,"manualUrl":null,"actuallyTime":null,"cluster":null,"relationSystem":null,"nodeSpec":null,"version":"V1","appDomain":null,"appName":null,"productLine":null,"bizUnit":"0","bizScopeType":"0","bizScope":"0","syncNextSystem":null,"externalProductName":null,"productVendor":null,"implVendor":null,"licenseMode":null,"licenseDescription":null,"priorityLevel":"0","serviceHours":"","description":null,"flowDomain":"","mobile":false}
2025-09-04 16:46:01.040 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | filteredBusinessData={}
2025-09-04 16:46:01.040 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=f43a5372-6e8f-42d8-9c21-0a2bd3fa47b2
2025-09-04 16:46:01.040 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | rawBusinessData={"id":"1930515640470077442","createdTime":1749105771000,"creatorId":"1923297212703780866","creatorName":"Lv_LingHui TS/TPD(吕凌晖)","updatedTime":1754966977000,"updaterId":"1923297212703780866","updaterName":"Lv_LingHui TS/TPD(吕凌晖)","tenantId":1,"cnName":"凌晖测试0605","cnSimpleName":"凌晖测试0605","enName":"lyutestapp","enSimpleName":"lyutestapp","cloudName":null,"cloudAppId":null,"cloudProjectId":null,"cloudProjectName":null,"cloudProjectAliasName":null,"cloudTenantId":null,"cloudTenantName":null,"cloudTenantAliasName":null,"cloudNamespaceId":null,"cloudNamespaceName":null,"cloudClusterId":null,"stageStatus":"Constructing","lockFlag":"0","appUrl":null,"remark":null,"detailPicUrl":null,"logoUrl":"https://tos.trinasolar.com/0lkj34hlov1zalhxz28f62/9c053cc3-0b51-4575-9914-41fab5db5fd2.jpg","titleType":null,"imgTitle":null,"title":null,"subTitle":null,"bgImage":null,"showType":null,"alignment":null,"appStyle":null,"appType":null,"isOuter":null,"expireTime":null,"urlType":null,"configItems":null,"homePage":null,"showHomepage":0,"manageUrl":null,"isMobile":0,"isManagePcAppMenu":0,"contraction":0,"platformEnName":null,"platformCnName":null,"ieSupported":0,"ieTip":"null","isControlFP":null,"isControlDP":null,"isContainerApp":null,"tenantShow":null,"businessDomain":"tasptest","tenantGroup":null,"isChangeTenant":null,"appSysType":"1171262252406378535","appGroup":null,"appServiceLevel":"3","constructionType":"0","devLanguage":"1171262252406378573","teamName":"tasptest","gitlab":true,"devops":true,"apm":true,"code":"c9d629a3-12d2-4458-b98e-93b23841b305","accessUrl":null,"manualUrl":null,"actuallyTime":null,"cluster":null,"relationSystem":null,"nodeSpec":null,"version":"V1","appDomain":null,"appName":null,"productLine":null,"bizUnit":"0","bizScopeType":"0","bizScope":"0","syncNextSystem":null,"externalProductName":null,"productVendor":null,"implVendor":null,"licenseMode":null,"licenseDescription":null,"priorityLevel":"0","serviceHours":"","description":null,"flowDomain":"","mobile":false}
2025-09-04 16:46:01.040 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | filteredBusinessData={}
2025-09-04 16:46:01.040 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=c9d629a3-12d2-4458-b98e-93b23841b305
2025-09-04 16:46:01.040 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | rawBusinessData={"id":"1930812115544571906","createdTime":1749176456000,"creatorId":"1923294602840059905","creatorName":"Yang_Gang TS/TPD(杨刚)","updatedTime":1754966977000,"updaterId":"1923294602840059905","updaterName":"Yang_Gang TS/TPD(杨刚)","tenantId":1,"cnName":"开发测试专用","cnSimpleName":"开发测试专用","enName":"devuser","enSimpleName":"devuser","cloudName":null,"cloudAppId":null,"cloudProjectId":null,"cloudProjectName":null,"cloudProjectAliasName":null,"cloudTenantId":null,"cloudTenantName":null,"cloudTenantAliasName":null,"cloudNamespaceId":null,"cloudNamespaceName":null,"cloudClusterId":null,"stageStatus":"Constructing","lockFlag":"0","appUrl":null,"remark":"1111","detailPicUrl":null,"logoUrl":"https://tos.trinasolar.com/0lkj34hlov1zalhxz28f62/7f2a0e6c-13f1-4a98-a99d-9a29e64ba006.png","titleType":null,"imgTitle":null,"title":null,"subTitle":null,"bgImage":null,"showType":null,"alignment":null,"appStyle":null,"appType":null,"isOuter":null,"expireTime":null,"urlType":null,"configItems":null,"homePage":null,"showHomepage":0,"manageUrl":null,"isMobile":0,"isManagePcAppMenu":0,"contraction":0,"platformEnName":null,"platformCnName":null,"ieSupported":0,"ieTip":"null","isControlFP":null,"isControlDP":null,"isContainerApp":null,"tenantShow":null,"businessDomain":"tasptest","tenantGroup":null,"isChangeTenant":null,"appSysType":"1171262252406378535","appGroup":null,"appServiceLevel":"3","constructionType":"1","devLanguage":"1171262252406378576","teamName":"tasptest","gitlab":true,"devops":true,"apm":true,"code":"b4f62b75-df03-4431-a702-92efc567ec11","accessUrl":null,"manualUrl":null,"actuallyTime":null,"cluster":null,"relationSystem":null,"nodeSpec":null,"version":"V1","appDomain":null,"appName":null,"productLine":null,"bizUnit":"0","bizScopeType":"0","bizScope":"0","syncNextSystem":null,"externalProductName":null,"productVendor":null,"implVendor":null,"licenseMode":null,"licenseDescription":null,"priorityLevel":"0","serviceHours":"","description":null,"flowDomain":"","mobile":false}
2025-09-04 16:46:01.041 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | filteredBusinessData={}
2025-09-04 16:46:01.041 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=b4f62b75-df03-4431-a702-92efc567ec11
2025-09-04 16:46:01.041 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | rawBusinessData={"id":"1932021710732525570","createdTime":1749464846000,"creatorId":"1923297639180611586","creatorName":"刘顺","updatedTime":1754966977000,"updaterId":"1923297639180611586","updaterName":"刘顺","tenantId":1,"cnName":"测试0609","cnSimpleName":"测609","enName":"xtest0609","enSimpleName":"xt609","cloudName":null,"cloudAppId":null,"cloudProjectId":null,"cloudProjectName":null,"cloudProjectAliasName":null,"cloudTenantId":null,"cloudTenantName":null,"cloudTenantAliasName":null,"cloudNamespaceId":null,"cloudNamespaceName":null,"cloudClusterId":null,"stageStatus":"Constructing","lockFlag":"0","appUrl":null,"remark":"测试","detailPicUrl":null,"logoUrl":"https://tos.trinasolar.com/0lkj34hlov1zalhxz28f62/1d2f496e-ddb7-4c69-a967-13b8de4e5057.jpg","titleType":null,"imgTitle":null,"title":null,"subTitle":null,"bgImage":null,"showType":null,"alignment":null,"appStyle":null,"appType":null,"isOuter":null,"expireTime":null,"urlType":null,"configItems":null,"homePage":null,"showHomepage":0,"manageUrl":null,"isMobile":0,"isManagePcAppMenu":0,"contraction":0,"platformEnName":null,"platformCnName":null,"ieSupported":0,"ieTip":"null","isControlFP":null,"isControlDP":null,"isContainerApp":null,"tenantShow":null,"businessDomain":"tasptest","tenantGroup":null,"isChangeTenant":null,"appSysType":"1171262252406378535,1171262252406378534","appGroup":null,"appServiceLevel":"3","constructionType":"1","devLanguage":"1171262252406378573,1171262252406378576","teamName":"tasptest","gitlab":true,"devops":true,"apm":true,"code":"10aa12e9-985a-4a7f-84c5-4775faf75f05","accessUrl":null,"manualUrl":null,"actuallyTime":null,"cluster":null,"relationSystem":null,"nodeSpec":null,"version":"V1","appDomain":null,"appName":null,"productLine":null,"bizUnit":"0","bizScopeType":"0","bizScope":"0","syncNextSystem":null,"externalProductName":null,"productVendor":null,"implVendor":null,"licenseMode":null,"licenseDescription":null,"priorityLevel":"0","serviceHours":"","description":null,"flowDomain":"","mobile":false}
2025-09-04 16:46:01.041 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | filteredBusinessData={}
2025-09-04 16:46:01.041 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=10aa12e9-985a-4a7f-84c5-4775faf75f05
2025-09-04 16:46:01.041 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | rawBusinessData={"id":"1933422297667731458","createdTime":1749798772000,"creatorId":"1923294602840059905","creatorName":"Yang_Gang TS/TPD(杨刚)","updatedTime":1754966977000,"updaterId":"1923294602840059905","updaterName":"Yang_Gang TS/TPD(杨刚)","tenantId":1,"cnName":"测试应用01","cnSimpleName":"测试应用01","enName":"test-sys01","enSimpleName":"test-sys01","cloudName":null,"cloudAppId":null,"cloudProjectId":null,"cloudProjectName":null,"cloudProjectAliasName":null,"cloudTenantId":null,"cloudTenantName":null,"cloudTenantAliasName":null,"cloudNamespaceId":null,"cloudNamespaceName":null,"cloudClusterId":null,"stageStatus":"Constructing","lockFlag":"0","appUrl":null,"remark":null,"detailPicUrl":null,"logoUrl":"https://tos.trinasolar.com/0lkj34hlov1zalhxz28f62/4705f172-f62a-425e-8234-c976135eeea2.jpg","titleType":null,"imgTitle":null,"title":null,"subTitle":null,"bgImage":null,"showType":null,"alignment":null,"appStyle":null,"appType":null,"isOuter":null,"expireTime":null,"urlType":null,"configItems":null,"homePage":null,"showHomepage":0,"manageUrl":null,"isMobile":0,"isManagePcAppMenu":0,"contraction":0,"platformEnName":null,"platformCnName":null,"ieSupported":0,"ieTip":"null","isControlFP":null,"isControlDP":null,"isContainerApp":null,"tenantShow":null,"businessDomain":"tasptest","tenantGroup":null,"isChangeTenant":null,"appSysType":"1171262252406378534","appGroup":null,"appServiceLevel":"1","constructionType":"0","devLanguage":"1171262252406378573","teamName":"tasptest","gitlab":true,"devops":true,"apm":true,"code":"075350d7-db60-4535-bcd7-d1254864fe54","accessUrl":null,"manualUrl":null,"actuallyTime":null,"cluster":null,"relationSystem":null,"nodeSpec":null,"version":"V1","appDomain":null,"appName":null,"productLine":null,"bizUnit":"0","bizScopeType":"0","bizScope":"0","syncNextSystem":null,"externalProductName":null,"productVendor":null,"implVendor":null,"licenseMode":null,"licenseDescription":null,"priorityLevel":"0","serviceHours":"","description":null,"flowDomain":"","mobile":false}
2025-09-04 16:46:01.041 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | filteredBusinessData={}
2025-09-04 16:46:01.041 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=075350d7-db60-4535-bcd7-d1254864fe54
2025-09-04 16:46:01.042 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | rawBusinessData={"id":"1933424385026031618","createdTime":1749799269000,"creatorId":"1923295019330252801","creatorName":"Wu_GuoRong TS/TPD(吴国荣)","updatedTime":1754966977000,"updaterId":"1923295019330252801","updaterName":"Wu_GuoRong TS/TPD(吴国荣)","tenantId":1,"cnName":"测试别删-吴国荣","cnSimpleName":"测试","enName":"test_wgr","enSimpleName":"test_wgr","cloudName":null,"cloudAppId":null,"cloudProjectId":null,"cloudProjectName":null,"cloudProjectAliasName":null,"cloudTenantId":null,"cloudTenantName":null,"cloudTenantAliasName":null,"cloudNamespaceId":null,"cloudNamespaceName":null,"cloudClusterId":null,"stageStatus":"Constructing","lockFlag":"0","appUrl":null,"remark":null,"detailPicUrl":null,"logoUrl":"https://tos.trinasolar.com/0lkj34hlov1zalhxz28f62/df073e40-bc62-4dc0-ba07-8b575b7049c8.jpeg","titleType":null,"imgTitle":null,"title":null,"subTitle":null,"bgImage":null,"showType":null,"alignment":null,"appStyle":null,"appType":null,"isOuter":null,"expireTime":null,"urlType":null,"configItems":null,"homePage":null,"showHomepage":0,"manageUrl":null,"isMobile":0,"isManagePcAppMenu":0,"contraction":0,"platformEnName":null,"platformCnName":null,"ieSupported":0,"ieTip":"null","isControlFP":null,"isControlDP":null,"isContainerApp":null,"tenantShow":null,"businessDomain":"tasptest","tenantGroup":null,"isChangeTenant":null,"appSysType":"1171262252406378535","appGroup":null,"appServiceLevel":"3","constructionType":"0","devLanguage":"1171262252406378573","teamName":"tasptest","gitlab":true,"devops":true,"apm":true,"code":"bb270488-348b-4b1b-a63b-922f0b36df1d","accessUrl":null,"manualUrl":null,"actuallyTime":null,"cluster":null,"relationSystem":null,"nodeSpec":null,"version":"V1","appDomain":null,"appName":null,"productLine":null,"bizUnit":"0","bizScopeType":"0","bizScope":"0","syncNextSystem":null,"externalProductName":null,"productVendor":null,"implVendor":null,"licenseMode":null,"licenseDescription":null,"priorityLevel":"0","serviceHours":"","description":null,"flowDomain":"","mobile":false}
2025-09-04 16:46:01.042 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | filteredBusinessData={}
2025-09-04 16:46:01.042 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=bb270488-348b-4b1b-a63b-922f0b36df1d
2025-09-04 16:46:01.042 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | rawBusinessData={"id":"1935867022884311042","createdTime":1750381640000,"creatorId":"1923297639180611586","creatorName":"刘顺","updatedTime":1754966977000,"updaterId":"1923297639180611586","updaterName":"刘顺","tenantId":1,"cnName":"测试0620","cnSimpleName":"测0620","enName":"test0620","enSimpleName":"t0620","cloudName":null,"cloudAppId":null,"cloudProjectId":null,"cloudProjectName":null,"cloudProjectAliasName":null,"cloudTenantId":null,"cloudTenantName":null,"cloudTenantAliasName":null,"cloudNamespaceId":null,"cloudNamespaceName":null,"cloudClusterId":null,"stageStatus":"Constructing","lockFlag":"0","appUrl":null,"remark":"1","detailPicUrl":null,"logoUrl":"https://tos.trinasolar.com/0lkj34hlov1zalhxz28f62/b8239d2f-c319-4354-b40d-c4c9e1de0e46.JPG","titleType":null,"imgTitle":null,"title":null,"subTitle":null,"bgImage":null,"showType":null,"alignment":null,"appStyle":null,"appType":null,"isOuter":null,"expireTime":null,"urlType":null,"configItems":null,"homePage":null,"showHomepage":0,"manageUrl":null,"isMobile":0,"isManagePcAppMenu":0,"contraction":0,"platformEnName":null,"platformCnName":null,"ieSupported":0,"ieTip":"null","isControlFP":null,"isControlDP":null,"isContainerApp":null,"tenantShow":null,"businessDomain":"tasptest","tenantGroup":null,"isChangeTenant":null,"appSysType":"1171262252406378541","appGroup":null,"appServiceLevel":"1","constructionType":"0","devLanguage":"1171262252406378573","teamName":"tasptest","gitlab":true,"devops":true,"apm":true,"code":"800c39cc-3ba4-4078-a670-7197a7162e49","accessUrl":null,"manualUrl":null,"actuallyTime":null,"cluster":null,"relationSystem":null,"nodeSpec":null,"version":"V1","appDomain":null,"appName":null,"productLine":null,"bizUnit":"0","bizScopeType":"0","bizScope":"0","syncNextSystem":null,"externalProductName":null,"productVendor":null,"implVendor":null,"licenseMode":null,"licenseDescription":null,"priorityLevel":"0","serviceHours":"","description":null,"flowDomain":"","mobile":false}
2025-09-04 16:46:01.042 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | filteredBusinessData={}
2025-09-04 16:46:01.042 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=800c39cc-3ba4-4078-a670-7197a7162e49
2025-09-04 16:46:01.042 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | rawBusinessData={"id":"1937501192702234626","createdTime":1750771256000,"creatorId":"1923294602840059905","creatorName":"Yang_Gang TS/TPD(杨刚)","updatedTime":1754966977000,"updaterId":"1923294602840059905","updaterName":"Yang_Gang TS/TPD(杨刚)","tenantId":1,"cnName":"验证系统一类-624","cnSimpleName":"产供销平台1231","enName":"sjjcptrd","enSimpleName":"qwe","cloudName":null,"cloudAppId":null,"cloudProjectId":null,"cloudProjectName":null,"cloudProjectAliasName":null,"cloudTenantId":null,"cloudTenantName":null,"cloudTenantAliasName":null,"cloudNamespaceId":null,"cloudNamespaceName":null,"cloudClusterId":null,"stageStatus":"Constructing","lockFlag":"0","appUrl":null,"remark":"描述","detailPicUrl":null,"logoUrl":"https://tos.trinasolar.com/0lkj34hlov1zalhxz28f62/1ae836fd-2dfd-430c-91ce-6107959103fa.png","titleType":null,"imgTitle":null,"title":null,"subTitle":null,"bgImage":null,"showType":null,"alignment":null,"appStyle":null,"appType":null,"isOuter":null,"expireTime":null,"urlType":null,"configItems":null,"homePage":null,"showHomepage":0,"manageUrl":null,"isMobile":0,"isManagePcAppMenu":0,"contraction":0,"platformEnName":null,"platformCnName":null,"ieSupported":0,"ieTip":"null","isControlFP":null,"isControlDP":null,"isContainerApp":null,"tenantShow":null,"businessDomain":"tasptest","tenantGroup":null,"isChangeTenant":null,"appSysType":"1171262252406378534","appGroup":null,"appServiceLevel":"1","constructionType":"1","devLanguage":"1171262252406378573,1171262252406378575","teamName":"tasptest","gitlab":true,"devops":true,"apm":true,"code":"afed6346-8554-41f0-b8b1-12ef17a3ffc8","accessUrl":null,"manualUrl":null,"actuallyTime":null,"cluster":null,"relationSystem":null,"nodeSpec":null,"version":"V1","appDomain":null,"appName":null,"productLine":null,"bizUnit":"0","bizScopeType":"0","bizScope":"0","syncNextSystem":null,"externalProductName":null,"productVendor":null,"implVendor":null,"licenseMode":null,"licenseDescription":null,"priorityLevel":"0","serviceHours":"","description":null,"flowDomain":"","mobile":false}
2025-09-04 16:46:01.042 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | filteredBusinessData={}
2025-09-04 16:46:01.043 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=afed6346-8554-41f0-b8b1-12ef17a3ffc8
2025-09-04 16:46:01.043 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | rawBusinessData={"id":"1937501818286870530","createdTime":1750771405000,"creatorId":"1923294602840059905","creatorName":"Yang_Gang TS/TPD(杨刚)","updatedTime":1754966977000,"updaterId":"1923294602840059905","updaterName":"Yang_Gang TS/TPD(杨刚)","tenantId":1,"cnName":"测试系统","cnSimpleName":"测试","enName":"test-syste","enSimpleName":"test","cloudName":null,"cloudAppId":null,"cloudProjectId":null,"cloudProjectName":null,"cloudProjectAliasName":null,"cloudTenantId":null,"cloudTenantName":null,"cloudTenantAliasName":null,"cloudNamespaceId":null,"cloudNamespaceName":null,"cloudClusterId":null,"stageStatus":"Constructing","lockFlag":"0","appUrl":null,"remark":null,"detailPicUrl":null,"logoUrl":"https://tos.trinasolar.com/0lkj34hlov1zalhxz28f62/e924bbc5-2da9-4199-a218-a4f6b6d16e68.jpeg","titleType":null,"imgTitle":null,"title":null,"subTitle":null,"bgImage":null,"showType":null,"alignment":null,"appStyle":null,"appType":null,"isOuter":null,"expireTime":null,"urlType":null,"configItems":null,"homePage":null,"showHomepage":0,"manageUrl":null,"isMobile":0,"isManagePcAppMenu":0,"contraction":0,"platformEnName":null,"platformCnName":null,"ieSupported":0,"ieTip":"null","isControlFP":null,"isControlDP":null,"isContainerApp":null,"tenantShow":null,"businessDomain":"tasptest","tenantGroup":null,"isChangeTenant":null,"appSysType":"1171262252406378534","appGroup":null,"appServiceLevel":"1","constructionType":"1","devLanguage":"1171262252406378573","teamName":"tasptest","gitlab":true,"devops":true,"apm":true,"code":"c752df5a-855a-4a46-9287-b23695542e58","accessUrl":null,"manualUrl":null,"actuallyTime":null,"cluster":null,"relationSystem":null,"nodeSpec":null,"version":"V1","appDomain":null,"appName":null,"productLine":null,"bizUnit":"0","bizScopeType":"0","bizScope":"0","syncNextSystem":null,"externalProductName":null,"productVendor":null,"implVendor":null,"licenseMode":null,"licenseDescription":null,"priorityLevel":"0","serviceHours":"","description":null,"flowDomain":"","mobile":false}
2025-09-04 16:46:01.043 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | filteredBusinessData={}
2025-09-04 16:46:01.043 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=c752df5a-855a-4a46-9287-b23695542e58
2025-09-04 16:46:01.043 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | rawBusinessData={"id":"1939574068105379841","createdTime":1751265468000,"creatorId":"1923295019330252801","creatorName":"Wu_GuoRong TS/TPD(吴国荣)","updatedTime":1754966977000,"updaterId":"1923295019330252801","updaterName":"Wu_GuoRong TS/TPD(吴国荣)","tenantId":1,"cnName":"域名过期通知","cnSimpleName":"域名过期通知","enName":"domain","enSimpleName":"domain","cloudName":null,"cloudAppId":null,"cloudProjectId":null,"cloudProjectName":null,"cloudProjectAliasName":null,"cloudTenantId":null,"cloudTenantName":null,"cloudTenantAliasName":null,"cloudNamespaceId":null,"cloudNamespaceName":null,"cloudClusterId":null,"stageStatus":"Constructing","lockFlag":"0","appUrl":null,"remark":null,"detailPicUrl":null,"logoUrl":"https://tos.trinasolar.com/0lkj34hlov1zalhxz28f62/2800c750-e525-42af-ad31-c87c9efb5596.jpeg","titleType":null,"imgTitle":null,"title":null,"subTitle":null,"bgImage":null,"showType":null,"alignment":null,"appStyle":null,"appType":null,"isOuter":null,"expireTime":null,"urlType":null,"configItems":null,"homePage":null,"showHomepage":0,"manageUrl":null,"isMobile":0,"isManagePcAppMenu":0,"contraction":0,"platformEnName":null,"platformCnName":null,"ieSupported":0,"ieTip":"null","isControlFP":null,"isControlDP":null,"isContainerApp":null,"tenantShow":null,"businessDomain":"tasptest","tenantGroup":null,"isChangeTenant":null,"appSysType":"1171262252406378535","appGroup":null,"appServiceLevel":"3","constructionType":"0","devLanguage":"1171262252406378573","teamName":"tasptest","gitlab":true,"devops":true,"apm":true,"code":"e80c63a3-1a7e-4d9a-ad78-eb88e96edc6a","accessUrl":null,"manualUrl":null,"actuallyTime":null,"cluster":null,"relationSystem":null,"nodeSpec":null,"version":"V1","appDomain":null,"appName":null,"productLine":null,"bizUnit":"0","bizScopeType":"0","bizScope":"0","syncNextSystem":null,"externalProductName":null,"productVendor":null,"implVendor":null,"licenseMode":null,"licenseDescription":null,"priorityLevel":"0","serviceHours":"","description":null,"flowDomain":"","mobile":false}
2025-09-04 16:46:01.043 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | filteredBusinessData={}
2025-09-04 16:46:01.043 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=e80c63a3-1a7e-4d9a-ad78-eb88e96edc6a
2025-09-04 16:46:01.043 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | rawBusinessData={"id":"1945381520134569985","createdTime":1752650072000,"creatorId":"1923297639180611586","creatorName":"刘顺","updatedTime":1754966977000,"updaterId":"1923297639180611586","updaterName":"刘顺","tenantId":1,"cnName":"迭代5测试专用","cnSimpleName":"迭代5测试","enName":"tversion5","enSimpleName":"version5","cloudName":null,"cloudAppId":null,"cloudProjectId":null,"cloudProjectName":null,"cloudProjectAliasName":null,"cloudTenantId":null,"cloudTenantName":null,"cloudTenantAliasName":null,"cloudNamespaceId":null,"cloudNamespaceName":null,"cloudClusterId":null,"stageStatus":"Constructing","lockFlag":"0","appUrl":null,"remark":"迭代5测试使用","detailPicUrl":null,"logoUrl":"https://tos.trinasolar.com/0lkj34hlov1zalhxz28f62/f6710d36-3745-4316-a13f-53e3ec6d6a4d.JPG","titleType":null,"imgTitle":null,"title":null,"subTitle":null,"bgImage":null,"showType":null,"alignment":null,"appStyle":null,"appType":null,"isOuter":null,"expireTime":null,"urlType":null,"configItems":null,"homePage":null,"showHomepage":0,"manageUrl":null,"isMobile":0,"isManagePcAppMenu":0,"contraction":0,"platformEnName":null,"platformCnName":null,"ieSupported":0,"ieTip":"null","isControlFP":null,"isControlDP":null,"isContainerApp":null,"tenantShow":null,"businessDomain":"tasptest","tenantGroup":null,"isChangeTenant":null,"appSysType":"1171262252406378535,1171262252406378537,1171262252406378534","appGroup":null,"appServiceLevel":"2","constructionType":"1","devLanguage":"1171262252406378573,1171262252406378575","teamName":"tasptest","gitlab":true,"devops":true,"apm":true,"code":"3fe82700-e11a-43d5-9ef4-9d3eeeb5fe70","accessUrl":null,"manualUrl":null,"actuallyTime":null,"cluster":null,"relationSystem":null,"nodeSpec":null,"version":"V1","appDomain":null,"appName":null,"productLine":null,"bizUnit":"0","bizScopeType":"0","bizScope":"0","syncNextSystem":null,"externalProductName":null,"productVendor":null,"implVendor":null,"licenseMode":null,"licenseDescription":null,"priorityLevel":"0","serviceHours":"","description":null,"flowDomain":"","mobile":false}
2025-09-04 16:46:01.044 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | filteredBusinessData={}
2025-09-04 16:46:01.044 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=3fe82700-e11a-43d5-9ef4-9d3eeeb5fe70
2025-09-04 16:46:01.044 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | rawBusinessData={"id":"1945763759044096002","createdTime":1752741205000,"creatorId":"1923294602840059905","creatorName":"Yang_Gang TS/TPD(杨刚)","updatedTime":1754966977000,"updaterId":"1923294602840059905","updaterName":"Yang_Gang TS/TPD(杨刚)","tenantId":1,"cnName":"测试系统吴国荣","cnSimpleName":"测试系统吴国荣","enName":"test-wgr","enSimpleName":"test-wgr","cloudName":null,"cloudAppId":null,"cloudProjectId":null,"cloudProjectName":null,"cloudProjectAliasName":null,"cloudTenantId":null,"cloudTenantName":null,"cloudTenantAliasName":null,"cloudNamespaceId":null,"cloudNamespaceName":null,"cloudClusterId":null,"stageStatus":"Constructing","lockFlag":"0","appUrl":null,"remark":null,"detailPicUrl":null,"logoUrl":"https://tos.trinasolar.com/0lkj34hlov1zalhxz28f62/10fcc0c7-fa61-4665-aa36-ca013a653cd5.jpg","titleType":null,"imgTitle":null,"title":null,"subTitle":null,"bgImage":null,"showType":null,"alignment":null,"appStyle":null,"appType":null,"isOuter":null,"expireTime":null,"urlType":null,"configItems":null,"homePage":null,"showHomepage":0,"manageUrl":null,"isMobile":0,"isManagePcAppMenu":0,"contraction":0,"platformEnName":null,"platformCnName":null,"ieSupported":0,"ieTip":"null","isControlFP":null,"isControlDP":null,"isContainerApp":null,"tenantShow":null,"businessDomain":"tasptest","tenantGroup":null,"isChangeTenant":null,"appSysType":"1171262252406378534,1171262252406378535","appGroup":null,"appServiceLevel":"3","constructionType":"0","devLanguage":"1171262252406378573","teamName":"tasptest","gitlab":true,"devops":true,"apm":true,"code":"ccd459b5-4ae8-42c7-a711-f663ce3d60c6","accessUrl":null,"manualUrl":null,"actuallyTime":null,"cluster":null,"relationSystem":null,"nodeSpec":null,"version":"V1","appDomain":null,"appName":null,"productLine":null,"bizUnit":"0","bizScopeType":"0","bizScope":"0","syncNextSystem":null,"externalProductName":null,"productVendor":null,"implVendor":null,"licenseMode":null,"licenseDescription":null,"priorityLevel":"0","serviceHours":"","description":null,"flowDomain":"","mobile":false}
2025-09-04 16:46:01.044 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | filteredBusinessData={}
2025-09-04 16:46:01.044 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=ccd459b5-4ae8-42c7-a711-f663ce3d60c6
2025-09-04 16:46:01.045 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | rawBusinessData={"id":"1947112524263415810","createdTime":1753062776000,"creatorId":"1923297639180611586","creatorName":"刘顺","updatedTime":1754966977000,"updaterId":"1923297639180611586","updaterName":"刘顺","tenantId":1,"cnName":"测试0721","cnSimpleName":"测试","enName":"test0721-1","enSimpleName":"test","cloudName":null,"cloudAppId":null,"cloudProjectId":null,"cloudProjectName":null,"cloudProjectAliasName":null,"cloudTenantId":null,"cloudTenantName":null,"cloudTenantAliasName":null,"cloudNamespaceId":null,"cloudNamespaceName":null,"cloudClusterId":null,"stageStatus":"Constructing","lockFlag":"0","appUrl":null,"remark":null,"detailPicUrl":null,"logoUrl":"https://tos.trinasolar.com/0lkj34hlov1zalhxz28f62/179c0e51-04c2-495e-a563-465f86b0aeef.JPG","titleType":null,"imgTitle":null,"title":null,"subTitle":null,"bgImage":null,"showType":null,"alignment":null,"appStyle":null,"appType":null,"isOuter":null,"expireTime":null,"urlType":null,"configItems":null,"homePage":null,"showHomepage":0,"manageUrl":null,"isMobile":0,"isManagePcAppMenu":0,"contraction":0,"platformEnName":null,"platformCnName":null,"ieSupported":0,"ieTip":"null","isControlFP":null,"isControlDP":null,"isContainerApp":null,"tenantShow":null,"businessDomain":"tasptest","tenantGroup":null,"isChangeTenant":null,"appSysType":"1171262252406378534","appGroup":null,"appServiceLevel":"3","constructionType":"1","devLanguage":"1171262252406378573","teamName":"tasptest","gitlab":true,"devops":true,"apm":true,"code":"f7efa9a7-3218-4ad8-8c50-0d6e1ffa7268","accessUrl":null,"manualUrl":null,"actuallyTime":null,"cluster":null,"relationSystem":null,"nodeSpec":null,"version":"V1","appDomain":null,"appName":null,"productLine":null,"bizUnit":"0","bizScopeType":"0","bizScope":"0","syncNextSystem":null,"externalProductName":null,"productVendor":null,"implVendor":null,"licenseMode":null,"licenseDescription":null,"priorityLevel":"0","serviceHours":"","description":null,"flowDomain":"","mobile":false}
2025-09-04 16:46:01.045 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | filteredBusinessData={}
2025-09-04 16:46:01.045 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=f7efa9a7-3218-4ad8-8c50-0d6e1ffa7268
2025-09-04 16:46:01.045 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | rawBusinessData={"id":"1950094316280283138","createdTime":1753773691000,"creatorId":"1923297639180611586","creatorName":"刘顺","updatedTime":1754966977000,"updaterId":"1923297639180611586","updaterName":"刘顺","tenantId":1,"cnName":"测试729","cnSimpleName":"测试","enName":"test729","enSimpleName":"test729","cloudName":null,"cloudAppId":null,"cloudProjectId":null,"cloudProjectName":null,"cloudProjectAliasName":null,"cloudTenantId":null,"cloudTenantName":null,"cloudTenantAliasName":null,"cloudNamespaceId":null,"cloudNamespaceName":null,"cloudClusterId":null,"stageStatus":"Constructing","lockFlag":"0","appUrl":null,"remark":"应用描述在信息","detailPicUrl":null,"logoUrl":"https://tos.trinasolar.com/0lkj34hlov1zalhxz28f62/e630be5d-5466-4937-98c4-d96fdc33e6c6.png","titleType":null,"imgTitle":null,"title":null,"subTitle":null,"bgImage":null,"showType":null,"alignment":null,"appStyle":null,"appType":null,"isOuter":null,"expireTime":null,"urlType":null,"configItems":null,"homePage":null,"showHomepage":0,"manageUrl":null,"isMobile":0,"isManagePcAppMenu":0,"contraction":0,"platformEnName":null,"platformCnName":null,"ieSupported":0,"ieTip":"null","isControlFP":null,"isControlDP":null,"isContainerApp":null,"tenantShow":null,"businessDomain":"tasptest","tenantGroup":null,"isChangeTenant":null,"appSysType":"1171262252406378534,1171262252406378540,1171262252406378541","appGroup":null,"appServiceLevel":"3","constructionType":"1","devLanguage":"1171262252406378573,1171262252406378574","teamName":"tasptest","gitlab":true,"devops":true,"apm":true,"code":"84ef8fb7-694d-4dc2-a6d9-ca149ea418f4","accessUrl":null,"manualUrl":null,"actuallyTime":null,"cluster":null,"relationSystem":null,"nodeSpec":null,"version":"V1","appDomain":null,"appName":null,"productLine":null,"bizUnit":"0","bizScopeType":"0","bizScope":"0","syncNextSystem":null,"externalProductName":null,"productVendor":null,"implVendor":null,"licenseMode":null,"licenseDescription":null,"priorityLevel":"0","serviceHours":"","description":null,"flowDomain":"","mobile":false}
2025-09-04 16:46:01.045 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | filteredBusinessData={}
2025-09-04 16:46:01.045 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=84ef8fb7-694d-4dc2-a6d9-ca149ea418f4
2025-09-04 16:46:01.045 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | rawBusinessData={"id":"1953330380073181186","createdTime":1754545228000,"creatorId":"1923294602840059905","creatorName":"Yang_Gang TS/TPD(杨刚)","updatedTime":1754966977000,"updaterId":"1923294602840059905","updaterName":"Yang_Gang TS/TPD(杨刚)","tenantId":1,"cnName":"测试807-1","cnSimpleName":"测试","enName":"test807-1","enSimpleName":"test","cloudName":null,"cloudAppId":null,"cloudProjectId":null,"cloudProjectName":null,"cloudProjectAliasName":null,"cloudTenantId":null,"cloudTenantName":null,"cloudTenantAliasName":null,"cloudNamespaceId":null,"cloudNamespaceName":null,"cloudClusterId":null,"stageStatus":"Constructing","lockFlag":"0","appUrl":null,"remark":null,"detailPicUrl":null,"logoUrl":"","titleType":null,"imgTitle":null,"title":null,"subTitle":null,"bgImage":null,"showType":null,"alignment":null,"appStyle":null,"appType":null,"isOuter":null,"expireTime":null,"urlType":null,"configItems":null,"homePage":null,"showHomepage":0,"manageUrl":null,"isMobile":0,"isManagePcAppMenu":0,"contraction":0,"platformEnName":null,"platformCnName":null,"ieSupported":0,"ieTip":"null","isControlFP":null,"isControlDP":null,"isContainerApp":null,"tenantShow":null,"businessDomain":"tasptest","tenantGroup":null,"isChangeTenant":null,"appSysType":"1,2,3","appGroup":"apag","appServiceLevel":"3","constructionType":"Lowcode","devLanguage":"lang_python","teamName":"tasptest","gitlab":true,"devops":true,"apm":true,"code":"testcase807-1","accessUrl":null,"manualUrl":null,"actuallyTime":null,"cluster":null,"relationSystem":null,"nodeSpec":null,"version":"","appDomain":"mbtd","appName":"apa","productLine":null,"bizUnit":"ess","bizScopeType":"ms","bizScope":"cn","syncNextSystem":"devops,gitlab","externalProductName":null,"productVendor":null,"implVendor":null,"licenseMode":null,"licenseDescription":null,"priorityLevel":null,"serviceHours":"7*24","description":null,"flowDomain":null,"mobile":false}
2025-09-04 16:46:01.046 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | filteredBusinessData={}
2025-09-04 16:46:01.046 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=testcase807-1
2025-09-04 16:46:01.046 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | rawBusinessData={"id":"1953332570418421761","createdTime":1754545751000,"creatorId":"1923297639180611586","creatorName":"刘顺","updatedTime":1754966977000,"updaterId":"1923297639180611586","updaterName":"刘顺","tenantId":1,"cnName":"测试0807-1","cnSimpleName":"测试","enName":"test0807-1","enSimpleName":"test","cloudName":null,"cloudAppId":null,"cloudProjectId":null,"cloudProjectName":null,"cloudProjectAliasName":null,"cloudTenantId":null,"cloudTenantName":null,"cloudTenantAliasName":null,"cloudNamespaceId":null,"cloudNamespaceName":null,"cloudClusterId":null,"stageStatus":"Constructing","lockFlag":"0","appUrl":null,"remark":null,"detailPicUrl":null,"logoUrl":"","titleType":null,"imgTitle":null,"title":null,"subTitle":null,"bgImage":null,"showType":null,"alignment":null,"appStyle":null,"appType":null,"isOuter":null,"expireTime":null,"urlType":null,"configItems":null,"homePage":null,"showHomepage":0,"manageUrl":null,"isMobile":0,"isManagePcAppMenu":0,"contraction":0,"platformEnName":null,"platformCnName":null,"ieSupported":0,"ieTip":"null","isControlFP":null,"isControlDP":null,"isContainerApp":null,"tenantShow":null,"businessDomain":"tasptest","tenantGroup":null,"isChangeTenant":null,"appSysType":"1,2,3","appGroup":"apag","appServiceLevel":"3","constructionType":"Lowcode","devLanguage":"lang_python","teamName":"tasptest","gitlab":true,"devops":true,"apm":true,"code":"testcase0807-1","accessUrl":null,"manualUrl":null,"actuallyTime":null,"cluster":null,"relationSystem":null,"nodeSpec":null,"version":"","appDomain":"mbtd","appName":"apa","productLine":null,"bizUnit":"ess","bizScopeType":"ms","bizScope":"cn","syncNextSystem":"devops,gitlab","externalProductName":null,"productVendor":null,"implVendor":null,"licenseMode":null,"licenseDescription":null,"priorityLevel":null,"serviceHours":"5*8小时","description":null,"flowDomain":null,"mobile":false}
2025-09-04 16:46:01.046 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | filteredBusinessData={}
2025-09-04 16:46:01.046 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=testcase0807-1
2025-09-04 16:46:01.046 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | rawBusinessData={"id":"1953337485169631233","createdTime":1754546922000,"creatorId":"1923297639180611586","creatorName":"刘顺","updatedTime":1754966977000,"updaterId":"1923297639180611586","updaterName":"刘顺","tenantId":1,"cnName":"测试0807-2","cnSimpleName":"测试","enName":"test0807-2","enSimpleName":"test","cloudName":null,"cloudAppId":null,"cloudProjectId":null,"cloudProjectName":null,"cloudProjectAliasName":null,"cloudTenantId":null,"cloudTenantName":null,"cloudTenantAliasName":null,"cloudNamespaceId":null,"cloudNamespaceName":null,"cloudClusterId":null,"stageStatus":"Constructing","lockFlag":"0","appUrl":null,"remark":null,"detailPicUrl":null,"logoUrl":"","titleType":null,"imgTitle":null,"title":null,"subTitle":null,"bgImage":null,"showType":null,"alignment":null,"appStyle":null,"appType":null,"isOuter":null,"expireTime":null,"urlType":null,"configItems":null,"homePage":null,"showHomepage":0,"manageUrl":null,"isMobile":0,"isManagePcAppMenu":0,"contraction":0,"platformEnName":null,"platformCnName":null,"ieSupported":0,"ieTip":"null","isControlFP":null,"isControlDP":null,"isContainerApp":null,"tenantShow":null,"businessDomain":"tasptest","tenantGroup":null,"isChangeTenant":null,"appSysType":"1,2,7","appGroup":"isg","appServiceLevel":"3","constructionType":"Procurement","devLanguage":"lang_java,lang_dotnet,lang_python","teamName":"tasptest","gitlab":true,"devops":true,"apm":true,"code":"test0807-2-code","accessUrl":null,"manualUrl":null,"actuallyTime":null,"cluster":null,"relationSystem":null,"nodeSpec":null,"version":null,"appDomain":"mbtd","appName":"isa","productLine":"dp","bizUnit":"ess","bizScopeType":"ms","bizScope":"cn","syncNextSystem":"devops,gitlab","externalProductName":null,"productVendor":null,"implVendor":null,"licenseMode":"auth_multi","licenseDescription":"测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据","priorityLevel":null,"serviceHours":"5*8小时","description":null,"flowDomain":null,"mobile":false}
2025-09-04 16:46:01.047 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | filteredBusinessData={}
2025-09-04 16:46:01.047 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=test0807-2-code
2025-09-04 16:46:01.047 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | rawBusinessData={"id":"1953338810359980034","createdTime":1754547238000,"creatorId":"1923297639180611586","creatorName":"刘顺","updatedTime":1754966977000,"updaterId":"1923297639180611586","updaterName":"刘顺","tenantId":1,"cnName":"测试0807-3","cnSimpleName":"测试","enName":"test0807-3","enSimpleName":"test","cloudName":null,"cloudAppId":null,"cloudProjectId":null,"cloudProjectName":null,"cloudProjectAliasName":null,"cloudTenantId":null,"cloudTenantName":null,"cloudTenantAliasName":null,"cloudNamespaceId":null,"cloudNamespaceName":null,"cloudClusterId":null,"stageStatus":"Constructing","lockFlag":"0","appUrl":null,"remark":null,"detailPicUrl":null,"logoUrl":"","titleType":null,"imgTitle":null,"title":null,"subTitle":null,"bgImage":null,"showType":null,"alignment":null,"appStyle":null,"appType":null,"isOuter":null,"expireTime":null,"urlType":null,"configItems":null,"homePage":null,"showHomepage":0,"manageUrl":null,"isMobile":0,"isManagePcAppMenu":0,"contraction":0,"platformEnName":null,"platformCnName":null,"ieSupported":0,"ieTip":"null","isControlFP":null,"isControlDP":null,"isContainerApp":null,"tenantShow":null,"businessDomain":"ltc","tenantGroup":null,"isChangeTenant":null,"appSysType":"1,7,8","appGroup":"iitg","appServiceLevel":"3","constructionType":"Procurement","devLanguage":"lang_java,lang_dotnet","teamName":"ltc","gitlab":true,"devops":true,"apm":true,"code":"test0807-3-code","accessUrl":null,"manualUrl":null,"actuallyTime":null,"cluster":null,"relationSystem":null,"nodeSpec":null,"version":null,"appDomain":"mbtd","appName":"ehs","productLine":"dp","bizUnit":"sol","bizScopeType":"ms","bizScope":"us","syncNextSystem":"devops,gitlab","externalProductName":null,"productVendor":null,"implVendor":null,"licenseMode":"auth_multi","licenseDescription":null,"priorityLevel":null,"serviceHours":"7*24小时服务","description":null,"flowDomain":null,"mobile":false}
2025-09-04 16:46:01.047 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | filteredBusinessData={}
2025-09-04 16:46:01.047 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=test0807-3-code
2025-09-04 16:46:01.047 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | rawBusinessData={"id":"1953339768334819330","createdTime":1754547467000,"creatorId":"1923297639180611586","creatorName":"刘顺","updatedTime":1754966977000,"updaterId":"1923297639180611586","updaterName":"刘顺","tenantId":1,"cnName":"测试0807-4","cnSimpleName":"测试","enName":"test0807-4","enSimpleName":"test","cloudName":null,"cloudAppId":null,"cloudProjectId":null,"cloudProjectName":null,"cloudProjectAliasName":null,"cloudTenantId":null,"cloudTenantName":null,"cloudTenantAliasName":null,"cloudNamespaceId":null,"cloudNamespaceName":null,"cloudClusterId":null,"stageStatus":"Constructing","lockFlag":"0","appUrl":null,"remark":null,"detailPicUrl":null,"logoUrl":"","titleType":null,"imgTitle":null,"title":null,"subTitle":null,"bgImage":null,"showType":null,"alignment":null,"appStyle":null,"appType":null,"isOuter":null,"expireTime":null,"urlType":null,"configItems":null,"homePage":null,"showHomepage":0,"manageUrl":null,"isMobile":0,"isManagePcAppMenu":0,"contraction":0,"platformEnName":null,"platformCnName":null,"ieSupported":0,"ieTip":"null","isControlFP":null,"isControlDP":null,"isContainerApp":null,"tenantShow":null,"businessDomain":"tasptest","tenantGroup":null,"isChangeTenant":null,"appSysType":"8,7","appGroup":"dmg","appServiceLevel":"3","constructionType":"Procurement","devLanguage":"lang_js,lang_swift","teamName":"tasptest","gitlab":true,"devops":true,"apm":true,"code":"test0807-4-code","accessUrl":null,"manualUrl":null,"actuallyTime":null,"cluster":null,"relationSystem":null,"nodeSpec":null,"version":null,"appDomain":"mbtd","appName":"dp","productLine":null,"bizUnit":"back","bizScopeType":"im","bizScope":"id_jd","syncNextSystem":"devops,gitlab","externalProductName":null,"productVendor":null,"implVendor":null,"licenseMode":"auth_custom","licenseDescription":null,"priorityLevel":null,"serviceHours":"测试服务时间不符合建议字符规则","description":null,"flowDomain":null,"mobile":false}
2025-09-04 16:46:01.047 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | filteredBusinessData={}
2025-09-04 16:46:01.047 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=test0807-4-code
2025-09-04 16:46:01.048 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | rawBusinessData={"id":"1953340772178567170","createdTime":1754547706000,"creatorId":"1923297639180611586","creatorName":"刘顺","updatedTime":1754966977000,"updaterId":"1923297639180611586","updaterName":"刘顺","tenantId":1,"cnName":"测试0807-6","cnSimpleName":"测试","enName":"test0807-6","enSimpleName":"test","cloudName":null,"cloudAppId":null,"cloudProjectId":null,"cloudProjectName":null,"cloudProjectAliasName":null,"cloudTenantId":null,"cloudTenantName":null,"cloudTenantAliasName":null,"cloudNamespaceId":null,"cloudNamespaceName":null,"cloudClusterId":null,"stageStatus":"Constructing","lockFlag":"0","appUrl":null,"remark":"测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据","detailPicUrl":null,"logoUrl":"https://tos.trinasolar.com/0lkj34hlov1zalhxz28f62/313bff2e-8659-4e9a-962d-8547a08acd68.JPG","titleType":null,"imgTitle":null,"title":null,"subTitle":null,"bgImage":null,"showType":null,"alignment":null,"appStyle":null,"appType":null,"isOuter":null,"expireTime":null,"urlType":null,"configItems":null,"homePage":null,"showHomepage":0,"manageUrl":null,"isMobile":0,"isManagePcAppMenu":0,"contraction":0,"platformEnName":null,"platformCnName":null,"ieSupported":0,"ieTip":"null","isControlFP":null,"isControlDP":null,"isContainerApp":null,"tenantShow":null,"businessDomain":"tasptest","tenantGroup":null,"isChangeTenant":null,"appSysType":"1,7","appGroup":"pmg","appServiceLevel":"3","constructionType":"Procurement","devLanguage":"lang_java,lang_dotnet","teamName":"tasptest","gitlab":true,"devops":true,"apm":true,"code":"test0807-6-code","accessUrl":null,"manualUrl":null,"actuallyTime":null,"cluster":null,"relationSystem":null,"nodeSpec":null,"version":null,"appDomain":"mbtd","appName":"pma","productLine":"dap","bizUnit":"sol","bizScopeType":"other","bizScope":"eu_reg","syncNextSystem":"devops,gitlab","externalProductName":null,"productVendor":null,"implVendor":null,"licenseMode":"auth_custom","licenseDescription":null,"priorityLevel":null,"serviceHours":"","description":"测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据测试数据","flowDomain":null,"mobile":false}
2025-09-04 16:46:01.048 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | filteredBusinessData={}
2025-09-04 16:46:01.048 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=test0807-6-code
2025-09-04 16:46:01.048 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | rawBusinessData={"id":"1953347933059260418","createdTime":1754549413000,"creatorId":"1923297639180611586","creatorName":"刘顺","updatedTime":1754966977000,"updaterId":"1923297639180611586","updaterName":"刘顺","tenantId":1,"cnName":"测试0807-7","cnSimpleName":"测试","enName":"test0807-7","enSimpleName":"test","cloudName":null,"cloudAppId":null,"cloudProjectId":null,"cloudProjectName":null,"cloudProjectAliasName":null,"cloudTenantId":null,"cloudTenantName":null,"cloudTenantAliasName":null,"cloudNamespaceId":null,"cloudNamespaceName":null,"cloudClusterId":null,"stageStatus":"Constructing","lockFlag":"0","appUrl":null,"remark":"BC","detailPicUrl":null,"logoUrl":"","titleType":null,"imgTitle":null,"title":null,"subTitle":null,"bgImage":null,"showType":null,"alignment":null,"appStyle":null,"appType":null,"isOuter":null,"expireTime":null,"urlType":null,"configItems":null,"homePage":null,"showHomepage":0,"manageUrl":null,"isMobile":0,"isManagePcAppMenu":0,"contraction":0,"platformEnName":null,"platformCnName":null,"ieSupported":0,"ieTip":"null","isControlFP":null,"isControlDP":null,"isContainerApp":null,"tenantShow":null,"businessDomain":"other","tenantGroup":null,"isChangeTenant":null,"appSysType":"1,7","appGroup":"dmg","appServiceLevel":"3","constructionType":"Procurement","devLanguage":"lang_java","teamName":"other","gitlab":true,"devops":true,"apm":true,"code":"test0807-7-code","accessUrl":null,"manualUrl":null,"actuallyTime":null,"cluster":null,"relationSystem":null,"nodeSpec":null,"version":"v1.0.0","appDomain":"mbtd","appName":"dp","productLine":null,"bizUnit":"ri","bizScopeType":"other","bizScope":"eu_reg","syncNextSystem":"devops,gitlab","externalProductName":"TASP","productVendor":"XY","implVendor":"XYKJ","licenseMode":"auth_custom","licenseDescription":"其它","priorityLevel":null,"serviceHours":"测试","description":"BA","flowDomain":"测试流程域","mobile":false}
2025-09-04 16:46:01.048 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | filteredBusinessData={}
2025-09-04 16:46:01.048 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=test0807-7-code
2025-09-04 16:46:01.048 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | rawBusinessData={"id":"1953695164203814913","createdTime":1754632200000,"creatorId":"1923297639180611586","creatorName":"刘顺","updatedTime":1754966977000,"updaterId":"1923297639180611586","updaterName":"刘顺","tenantId":1,"cnName":"测试0808-1","cnSimpleName":"缺陷修复1694","enName":"test0808-1","enSimpleName":"bugfix1694","cloudName":null,"cloudAppId":null,"cloudProjectId":null,"cloudProjectName":null,"cloudProjectAliasName":null,"cloudTenantId":null,"cloudTenantName":null,"cloudTenantAliasName":null,"cloudNamespaceId":null,"cloudNamespaceName":null,"cloudClusterId":null,"stageStatus":"Constructing","lockFlag":"0","appUrl":null,"remark":"测试数据 - 备注","detailPicUrl":null,"logoUrl":"https://tos.trinasolar.com/0lkj34hlov1zalhxz28f62/0e50928d-2d89-4a85-aed2-a44444ba8586.JPG","titleType":null,"imgTitle":null,"title":null,"subTitle":null,"bgImage":null,"showType":null,"alignment":null,"appStyle":null,"appType":null,"isOuter":null,"expireTime":null,"urlType":null,"configItems":null,"homePage":null,"showHomepage":0,"manageUrl":null,"isMobile":0,"isManagePcAppMenu":0,"contraction":0,"platformEnName":null,"platformCnName":null,"ieSupported":0,"ieTip":"null","isControlFP":null,"isControlDP":null,"isContainerApp":null,"tenantShow":null,"businessDomain":"tasptest","tenantGroup":null,"isChangeTenant":null,"appSysType":"1,7,8","appGroup":"tmg","appServiceLevel":"3","constructionType":"Procurement","devLanguage":"lang_java,lang_dotnet,lang_python","teamName":"tasptest","gitlab":true,"devops":true,"apm":true,"code":"testbugfix","accessUrl":null,"manualUrl":null,"actuallyTime":null,"cluster":null,"relationSystem":null,"nodeSpec":null,"version":"v0.6","appDomain":"mbtd","appName":"pjm","productLine":"dp","bizUnit":"sol","bizScopeType":"im","bizScope":"id_jd","syncNextSystem":"devops,gitlab","externalProductName":"TASP","productVendor":"xykj","implVendor":"xykj-yys","licenseMode":"auth_annual","licenseDescription":"测试 - 授权方式说明","priorityLevel":null,"serviceHours":"5*8小时服务","description":"测试数据 - 系统定位描述","flowDomain":"测试 -流程域","mobile":false}
2025-09-04 16:46:01.048 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | filteredBusinessData={}
2025-09-04 16:46:01.048 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=testbugfix
2025-09-04 16:46:01.049 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | rawBusinessData={"id":"1953726096289533953","createdTime":1754639574000,"creatorId":"1923296746045517826","creatorName":"彭书友","updatedTime":1754966977000,"updaterId":"1923296746045517826","updaterName":"彭书友","tenantId":1,"cnName":"test11-客户服务平台","cnSimpleName":"test11-客户服务","enName":"test11Customer Service Plartform","enSimpleName":"test11csp","cloudName":null,"cloudAppId":null,"cloudProjectId":null,"cloudProjectName":null,"cloudProjectAliasName":null,"cloudTenantId":null,"cloudTenantName":null,"cloudTenantAliasName":null,"cloudNamespaceId":null,"cloudNamespaceName":null,"cloudClusterId":null,"stageStatus":"Constructing","lockFlag":"0","appUrl":null,"remark":"","detailPicUrl":null,"logoUrl":null,"titleType":null,"imgTitle":null,"title":null,"subTitle":null,"bgImage":null,"showType":null,"alignment":null,"appStyle":null,"appType":null,"isOuter":null,"expireTime":null,"urlType":null,"configItems":null,"homePage":null,"showHomepage":0,"manageUrl":null,"isMobile":0,"isManagePcAppMenu":0,"contraction":0,"platformEnName":null,"platformCnName":null,"ieSupported":0,"ieTip":"null","isControlFP":null,"isControlDP":null,"isContainerApp":null,"tenantShow":null,"businessDomain":"isc","tenantGroup":null,"isChangeTenant":null,"appSysType":"2,4,1,3","appGroup":"dimg","appServiceLevel":"1","constructionType":"SelfDevelopment","devLanguage":"lang_java,lang_js,lang_swift","teamName":null,"gitlab":false,"devops":false,"apm":false,"code":null,"accessUrl":"https://customerservice.trinasolar.com/#/home","manualUrl":"","actuallyTime":1735574400000,"cluster":null,"relationSystem":null,"nodeSpec":null,"version":null,"appDomain":"mbtd","appName":"dma","productLine":"dp","bizUnit":"gen","bizScopeType":"other","bizScope":"中国区/欧洲区/中东非区/亚太区/北美区/拉美区","syncNextSystem":"devops,gitlab","externalProductName":"salesforce","productVendor":"德勤","implVendor":"德勤","licenseMode":"auth_annual","licenseDescription":null,"priorityLevel":null,"serviceHours":"7*24小时服务","description":"全球RTS及GCS进行客诉处理的后台管理平台。","flowDomain":"6","mobile":false}
2025-09-04 16:46:01.049 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | filteredBusinessData={}
2025-09-04 16:46:01.049 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=null
2025-09-04 16:46:01.049 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | rawBusinessData={"id":"1955075592196685826","createdTime":1754961319000,"creatorId":"1923297639180611586","creatorName":"刘顺","updatedTime":1754966977000,"updaterId":"1923297639180611586","updaterName":"刘顺","tenantId":1,"cnName":"测试0811-1","cnSimpleName":"测试","enName":"test0811-1","enSimpleName":"test","cloudName":null,"cloudAppId":null,"cloudProjectId":null,"cloudProjectName":null,"cloudProjectAliasName":null,"cloudTenantId":null,"cloudTenantName":null,"cloudTenantAliasName":null,"cloudNamespaceId":null,"cloudNamespaceName":null,"cloudClusterId":null,"stageStatus":"Constructing","lockFlag":"0","appUrl":null,"remark":"测试-描述","detailPicUrl":null,"logoUrl":null,"titleType":null,"imgTitle":null,"title":null,"subTitle":null,"bgImage":null,"showType":null,"alignment":null,"appStyle":null,"appType":null,"isOuter":null,"expireTime":null,"urlType":null,"configItems":null,"homePage":null,"showHomepage":0,"manageUrl":null,"isMobile":0,"isManagePcAppMenu":0,"contraction":0,"platformEnName":null,"platformCnName":null,"ieSupported":0,"ieTip":"null","isControlFP":null,"isControlDP":null,"isContainerApp":null,"tenantShow":null,"businessDomain":"other","tenantGroup":null,"isChangeTenant":null,"appSysType":"2,3","appGroup":"dmg","appServiceLevel":"3","constructionType":"SelfDevelopment","devLanguage":"lang_java,lang_js","teamName":null,"gitlab":false,"devops":false,"apm":false,"code":null,"accessUrl":"https://newipaas-mgr-uat.trinasolar.com/ipaassub/ipaassub","manualUrl":"https://newipaas-mgr-uat.trinasolar.com/index","actuallyTime":1754841600000,"cluster":null,"relationSystem":null,"nodeSpec":null,"version":null,"appDomain":"mbtd","appName":"dp","productLine":"lc","bizUnit":"ess","bizScopeType":"ms","bizScope":"中国区","syncNextSystem":"devops,gitlab","externalProductName":"","productVendor":"","implVendor":"","licenseMode":"","licenseDescription":null,"priorityLevel":null,"serviceHours":"测试服务时间","description":"测试导入1","flowDomain":"6","mobile":false}
2025-09-04 16:46:01.049 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | filteredBusinessData={}
2025-09-04 16:46:01.049 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=null
2025-09-04 16:46:01.049 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | rawBusinessData={"id":"1955076233614819329","createdTime":1754961472000,"creatorId":"1923297639180611586","creatorName":"刘顺","updatedTime":1754966977000,"updaterId":"1923297639180611586","updaterName":"刘顺","tenantId":1,"cnName":"测试0811-2","cnSimpleName":"测试","enName":"test0811-2","enSimpleName":"test","cloudName":null,"cloudAppId":null,"cloudProjectId":null,"cloudProjectName":null,"cloudProjectAliasName":null,"cloudTenantId":null,"cloudTenantName":null,"cloudTenantAliasName":null,"cloudNamespaceId":null,"cloudNamespaceName":null,"cloudClusterId":null,"stageStatus":"Constructing","lockFlag":"0","appUrl":null,"remark":"测试-描述","detailPicUrl":null,"logoUrl":null,"titleType":null,"imgTitle":null,"title":null,"subTitle":null,"bgImage":null,"showType":null,"alignment":null,"appStyle":null,"appType":null,"isOuter":null,"expireTime":null,"urlType":null,"configItems":null,"homePage":null,"showHomepage":0,"manageUrl":null,"isMobile":0,"isManagePcAppMenu":0,"contraction":0,"platformEnName":null,"platformCnName":null,"ieSupported":0,"ieTip":"null","isControlFP":null,"isControlDP":null,"isContainerApp":null,"tenantShow":null,"businessDomain":"other","tenantGroup":null,"isChangeTenant":null,"appSysType":"2,3","appGroup":"dmg","appServiceLevel":"3","constructionType":"SelfDevelopment","devLanguage":"lang_java,lang_js","teamName":null,"gitlab":false,"devops":false,"apm":false,"code":null,"accessUrl":"https://newipaas-mgr-uat.trinasolar.com/ipaassub/ipaassub","manualUrl":"https://newipaas-mgr-uat.trinasolar.com/index","actuallyTime":1754841600000,"cluster":null,"relationSystem":null,"nodeSpec":null,"version":null,"appDomain":"mbtd","appName":"dp","productLine":"lc","bizUnit":"ess","bizScopeType":"ms","bizScope":"中国区","syncNextSystem":"devops,gitlab","externalProductName":"","productVendor":"","implVendor":"","licenseMode":"","licenseDescription":null,"priorityLevel":null,"serviceHours":"测试服务时间","description":"测试导入1","flowDomain":"6","mobile":false}
2025-09-04 16:46:01.049 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | filteredBusinessData={}
2025-09-04 16:46:01.049 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=null
2025-09-04 16:46:01.049 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | rawBusinessData={"id":"1955098332759552001","createdTime":1754966741000,"creatorId":"1923297142012981250","creatorName":"王洁","updatedTime":1754966977000,"updaterId":"1923297142012981250","updaterName":"王洁","tenantId":1,"cnName":"测试0811-3","cnSimpleName":"测试","enName":"test0811-3","enSimpleName":"test","cloudName":null,"cloudAppId":null,"cloudProjectId":null,"cloudProjectName":null,"cloudProjectAliasName":null,"cloudTenantId":null,"cloudTenantName":null,"cloudTenantAliasName":null,"cloudNamespaceId":null,"cloudNamespaceName":null,"cloudClusterId":null,"stageStatus":"Constructing","lockFlag":"0","appUrl":null,"remark":"测试-描述","detailPicUrl":null,"logoUrl":null,"titleType":null,"imgTitle":null,"title":null,"subTitle":null,"bgImage":null,"showType":null,"alignment":null,"appStyle":null,"appType":null,"isOuter":null,"expireTime":null,"urlType":null,"configItems":null,"homePage":null,"showHomepage":0,"manageUrl":null,"isMobile":0,"isManagePcAppMenu":0,"contraction":0,"platformEnName":null,"platformCnName":null,"ieSupported":0,"ieTip":"null","isControlFP":null,"isControlDP":null,"isContainerApp":null,"tenantShow":null,"businessDomain":"other","tenantGroup":null,"isChangeTenant":null,"appSysType":"2,3","appGroup":"dmg","appServiceLevel":"3","constructionType":"Procurement","devLanguage":"lang_java,lang_js","teamName":null,"gitlab":false,"devops":false,"apm":false,"code":null,"accessUrl":"https://newipaas-mgr-uat.trinasolar.com/ipaassub/ipaassub","manualUrl":"https://newipaas-mgr-uat.trinasolar.com/index","actuallyTime":1754841600000,"cluster":null,"relationSystem":null,"nodeSpec":null,"version":null,"appDomain":"mbtd","appName":"dp","productLine":"lc","bizUnit":"ess","bizScopeType":"ms","bizScope":"中国区","syncNextSystem":"devops,gitlab","externalProductName":"测试-外采产品名称","productVendor":"测试-产品方","implVendor":"测试-产品实施方","licenseMode":"auth_annual","licenseDescription":null,"priorityLevel":null,"serviceHours":"测试服务时间","description":"测试导入1","flowDomain":"6","mobile":false}
2025-09-04 16:46:01.050 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | filteredBusinessData={}
2025-09-04 16:46:01.050 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=null
2025-09-04 16:46:01.050 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | rawBusinessData={"id":"1955106331708321793","createdTime":1754968648000,"creatorId":"1923297639180611586","creatorName":"刘顺","updatedTime":1754968648000,"updaterId":"1923297639180611586","updaterName":"刘顺","tenantId":1,"cnName":"测试0811-4","cnSimpleName":"测试","enName":"test0811-4","enSimpleName":"test","cloudName":null,"cloudAppId":null,"cloudProjectId":null,"cloudProjectName":null,"cloudProjectAliasName":null,"cloudTenantId":null,"cloudTenantName":null,"cloudTenantAliasName":null,"cloudNamespaceId":null,"cloudNamespaceName":null,"cloudClusterId":null,"stageStatus":null,"lockFlag":"0","appUrl":null,"remark":"测试-描述","detailPicUrl":null,"logoUrl":null,"titleType":null,"imgTitle":null,"title":null,"subTitle":null,"bgImage":null,"showType":null,"alignment":null,"appStyle":null,"appType":null,"isOuter":null,"expireTime":null,"urlType":null,"configItems":null,"homePage":null,"showHomepage":0,"manageUrl":null,"isMobile":0,"isManagePcAppMenu":0,"contraction":0,"platformEnName":null,"platformCnName":null,"ieSupported":0,"ieTip":"null","isControlFP":null,"isControlDP":null,"isContainerApp":null,"tenantShow":null,"businessDomain":"other","tenantGroup":null,"isChangeTenant":null,"appSysType":"2,3","appGroup":"dmg","appServiceLevel":"3","constructionType":"Procurement","devLanguage":"lang_java,lang_js","teamName":null,"gitlab":false,"devops":false,"apm":false,"code":null,"accessUrl":"https://newipaas-mgr-uat.trinasolar.com/ipaassub/ipaassub","manualUrl":"https://newipaas-mgr-uat.trinasolar.com/index","actuallyTime":1754841600000,"cluster":null,"relationSystem":null,"nodeSpec":null,"version":null,"appDomain":"mbtd","appName":"dp","productLine":"lc","bizUnit":"ess","bizScopeType":"ms","bizScope":"中国区","syncNextSystem":"devops,gitlab","externalProductName":"测试-外采产品名称","productVendor":"测试-产品方","implVendor":"测试-产品实施方","licenseMode":"auth_annual","licenseDescription":null,"priorityLevel":null,"serviceHours":"测试服务时间","description":"测试导入4","flowDomain":"6","mobile":false}
2025-09-04 16:46:01.050 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | filteredBusinessData={}
2025-09-04 16:46:01.050 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=null
2025-09-04 16:46:01.050 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | rawBusinessData={"id":"1955114404392955905","createdTime":1754970573000,"creatorId":"1923297639180611586","creatorName":"刘顺","updatedTime":1754970573000,"updaterId":"1923297639180611586","updaterName":"刘顺","tenantId":1,"cnName":"测试0811-2-1","cnSimpleName":"测试","enName":"test0811-2-1","enSimpleName":"test2-1","cloudName":null,"cloudAppId":null,"cloudProjectId":null,"cloudProjectName":null,"cloudProjectAliasName":null,"cloudTenantId":null,"cloudTenantName":null,"cloudTenantAliasName":null,"cloudNamespaceId":null,"cloudNamespaceName":null,"cloudClusterId":null,"stageStatus":null,"lockFlag":"0","appUrl":null,"remark":"测试-描述","detailPicUrl":null,"logoUrl":null,"titleType":null,"imgTitle":null,"title":null,"subTitle":null,"bgImage":null,"showType":null,"alignment":null,"appStyle":null,"appType":null,"isOuter":null,"expireTime":null,"urlType":null,"configItems":null,"homePage":null,"showHomepage":0,"manageUrl":null,"isMobile":0,"isManagePcAppMenu":0,"contraction":0,"platformEnName":null,"platformCnName":null,"ieSupported":0,"ieTip":"null","isControlFP":null,"isControlDP":null,"isContainerApp":null,"tenantShow":null,"businessDomain":"tasptest","tenantGroup":null,"isChangeTenant":null,"appSysType":"2,3","appGroup":"dmg","appServiceLevel":"3","constructionType":"SelfDevelopment","devLanguage":"lang_java,lang_js","teamName":null,"gitlab":false,"devops":false,"apm":false,"code":null,"accessUrl":"https://newipaas-mgr-uat.trinasolar.com/ipaassub/ipaassub","manualUrl":"https://newipaas-mgr-uat.trinasolar.com/index","actuallyTime":1754841600000,"cluster":null,"relationSystem":null,"nodeSpec":null,"version":null,"appDomain":"mbtd","appName":"dp","productLine":"lc","bizUnit":"ess","bizScopeType":"ms","bizScope":"中国区","syncNextSystem":"devops,gitlab","externalProductName":"","productVendor":"","implVendor":"","licenseMode":"","licenseDescription":null,"priorityLevel":null,"serviceHours":"测试服务时间","description":"测试导入1","flowDomain":"6","mobile":false}
2025-09-04 16:46:01.050 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | filteredBusinessData={}
2025-09-04 16:46:01.050 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=null
2025-09-04 16:46:01.050 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | rawBusinessData={"id":"1955155703284330498","createdTime":1754980419000,"creatorId":"1923297639180611586","creatorName":"刘顺","updatedTime":1754980419000,"updaterId":"1923297639180611586","updaterName":"刘顺","tenantId":1,"cnName":"测试810-1","cnSimpleName":"测试","enName":"test810-1","enSimpleName":"test","cloudName":null,"cloudAppId":null,"cloudProjectId":null,"cloudProjectName":null,"cloudProjectAliasName":null,"cloudTenantId":null,"cloudTenantName":null,"cloudTenantAliasName":null,"cloudNamespaceId":null,"cloudNamespaceName":null,"cloudClusterId":null,"stageStatus":"Planning","lockFlag":"0","appUrl":null,"remark":null,"detailPicUrl":null,"logoUrl":"","titleType":null,"imgTitle":null,"title":null,"subTitle":null,"bgImage":null,"showType":null,"alignment":null,"appStyle":null,"appType":null,"isOuter":null,"expireTime":null,"urlType":null,"configItems":null,"homePage":null,"showHomepage":0,"manageUrl":null,"isMobile":0,"isManagePcAppMenu":0,"contraction":0,"platformEnName":null,"platformCnName":null,"ieSupported":0,"ieTip":"null","isControlFP":null,"isControlDP":null,"isContainerApp":null,"tenantShow":null,"businessDomain":"tasptest","tenantGroup":null,"isChangeTenant":null,"appSysType":"2,3","appGroup":"apag","appServiceLevel":"3","constructionType":"SelfDevelopment","devLanguage":"lang_java,lang_dotnet","teamName":"tasptest","gitlab":false,"devops":false,"apm":false,"code":"test","accessUrl":null,"manualUrl":null,"actuallyTime":null,"cluster":null,"relationSystem":null,"nodeSpec":null,"version":null,"appDomain":"mbtd","appName":"apa","productLine":"ebap","bizUnit":"pv","bizScopeType":"ms","bizScope":"cn","syncNextSystem":"devops,gitlab","externalProductName":null,"productVendor":null,"implVendor":null,"licenseMode":null,"licenseDescription":null,"priorityLevel":null,"serviceHours":null,"description":null,"flowDomain":null,"mobile":false}
2025-09-04 16:46:01.051 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | filteredBusinessData={}
2025-09-04 16:46:01.051 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=test
2025-09-04 16:46:01.051 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | rawBusinessData={"id":"1955156909226422273","createdTime":1754980707000,"creatorId":"1923297639180611586","creatorName":"刘顺","updatedTime":1754980707000,"updaterId":"1923297639180611586","updaterName":"刘顺","tenantId":1,"cnName":"测试0810-2","cnSimpleName":"测试","enName":"test0810-2","enSimpleName":"test2-1","cloudName":null,"cloudAppId":null,"cloudProjectId":null,"cloudProjectName":null,"cloudProjectAliasName":null,"cloudTenantId":null,"cloudTenantName":null,"cloudTenantAliasName":null,"cloudNamespaceId":null,"cloudNamespaceName":null,"cloudClusterId":null,"stageStatus":null,"lockFlag":"0","appUrl":null,"remark":"测试-描述","detailPicUrl":null,"logoUrl":null,"titleType":null,"imgTitle":null,"title":null,"subTitle":null,"bgImage":null,"showType":null,"alignment":null,"appStyle":null,"appType":null,"isOuter":null,"expireTime":null,"urlType":null,"configItems":null,"homePage":null,"showHomepage":0,"manageUrl":null,"isMobile":0,"isManagePcAppMenu":0,"contraction":0,"platformEnName":null,"platformCnName":null,"ieSupported":0,"ieTip":"null","isControlFP":null,"isControlDP":null,"isContainerApp":null,"tenantShow":null,"businessDomain":"tasptest","tenantGroup":null,"isChangeTenant":null,"appSysType":"2,3","appGroup":"dmg","appServiceLevel":"3","constructionType":"SelfDevelopment","devLanguage":"lang_java,lang_js","teamName":null,"gitlab":false,"devops":false,"apm":false,"code":null,"accessUrl":"https://newipaas-mgr-uat.trinasolar.com/ipaassub/ipaassub","manualUrl":"https://newipaas-mgr-uat.trinasolar.com/index","actuallyTime":1754841600000,"cluster":null,"relationSystem":null,"nodeSpec":null,"version":null,"appDomain":"mbtd","appName":"dp","productLine":"lc","bizUnit":"ess","bizScopeType":"ms","bizScope":"中国区","syncNextSystem":"devops,gitlab","externalProductName":"","productVendor":"","implVendor":"","licenseMode":"","licenseDescription":null,"priorityLevel":null,"serviceHours":"测试服务时间","description":"测试导入1","flowDomain":"6","mobile":false}
2025-09-04 16:46:01.051 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | filteredBusinessData={}
2025-09-04 16:46:01.051 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=null
2025-09-04 16:46:01.051 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | rawBusinessData={"id":"1955167077175619585","createdTime":1754983131000,"creatorId":"1923297639180611586","creatorName":"刘顺","updatedTime":1754983131000,"updaterId":"1923297639180611586","updaterName":"刘顺","tenantId":1,"cnName":"测试0810-3","cnSimpleName":"测试","enName":"test0810-3","enSimpleName":"test-3","cloudName":null,"cloudAppId":null,"cloudProjectId":null,"cloudProjectName":null,"cloudProjectAliasName":null,"cloudTenantId":null,"cloudTenantName":null,"cloudTenantAliasName":null,"cloudNamespaceId":null,"cloudNamespaceName":null,"cloudClusterId":null,"stageStatus":"Planning","lockFlag":"0","appUrl":null,"remark":null,"detailPicUrl":null,"logoUrl":"","titleType":null,"imgTitle":null,"title":null,"subTitle":null,"bgImage":null,"showType":null,"alignment":null,"appStyle":null,"appType":null,"isOuter":null,"expireTime":null,"urlType":null,"configItems":null,"homePage":null,"showHomepage":0,"manageUrl":null,"isMobile":0,"isManagePcAppMenu":0,"contraction":0,"platformEnName":null,"platformCnName":null,"ieSupported":0,"ieTip":"null","isControlFP":null,"isControlDP":null,"isContainerApp":null,"tenantShow":null,"businessDomain":"ltc","tenantGroup":null,"isChangeTenant":null,"appSysType":"2,3","appGroup":"pmg","appServiceLevel":"3","constructionType":"Cooperative","devLanguage":"lang_java,lang_dotnet","teamName":"ltc","gitlab":false,"devops":false,"apm":false,"code":"test","accessUrl":null,"manualUrl":null,"actuallyTime":null,"cluster":null,"relationSystem":null,"nodeSpec":null,"version":null,"appDomain":"mbtd","appName":"pma","productLine":"ebap","bizUnit":"sol","bizScopeType":"other","bizScope":"eu_reg","syncNextSystem":"devops,gitlab","externalProductName":null,"productVendor":null,"implVendor":null,"licenseMode":null,"licenseDescription":null,"priorityLevel":null,"serviceHours":null,"description":null,"flowDomain":null,"mobile":false}
2025-09-04 16:46:01.051 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | filteredBusinessData={}
2025-09-04 16:46:01.051 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=test
2025-09-04 16:46:01.051 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | rawBusinessData={"id":"1955171489268854785","createdTime":1754984183000,"creatorId":"1923297639180611586","creatorName":"刘顺","updatedTime":1754984183000,"updaterId":"1923297639180611586","updaterName":"刘顺","tenantId":1,"cnName":"测试0810-4","cnSimpleName":"测试","enName":"test0810-4","enSimpleName":"test2-1","cloudName":null,"cloudAppId":null,"cloudProjectId":null,"cloudProjectName":null,"cloudProjectAliasName":null,"cloudTenantId":null,"cloudTenantName":null,"cloudTenantAliasName":null,"cloudNamespaceId":null,"cloudNamespaceName":null,"cloudClusterId":null,"stageStatus":"Constructing","lockFlag":"0","appUrl":null,"remark":"测试-描述测试-描述测试-描述测试-描述测试-描述测试-描述测试-描述测试-描述测试-描述测试-描述测试-描述测试-描述测试-描述测试-描述测试-测试-描述测试-描述测试-描述测试-描述测试-测试-描述测试-描述测试-描述测试-描述测试-描述测试-描述测试-描述测试-描述测试-描述测试-描述测试-描述测试-描述测试-描述测试-描述测试-描述","detailPicUrl":null,"logoUrl":null,"titleType":null,"imgTitle":null,"title":null,"subTitle":null,"bgImage":null,"showType":null,"alignment":null,"appStyle":null,"appType":null,"isOuter":null,"expireTime":null,"urlType":null,"configItems":null,"homePage":null,"showHomepage":0,"manageUrl":null,"isMobile":0,"isManagePcAppMenu":0,"contraction":0,"platformEnName":null,"platformCnName":null,"ieSupported":0,"ieTip":"null","isControlFP":null,"isControlDP":null,"isContainerApp":null,"tenantShow":null,"businessDomain":"tasptest","tenantGroup":null,"isChangeTenant":null,"appSysType":"2,3","appGroup":"dmg","appServiceLevel":"3","constructionType":"SelfDevelopment","devLanguage":"lang_java,lang_js","teamName":"tasptest","gitlab":false,"devops":false,"apm":false,"code":"da","accessUrl":"https://newipaas-mgr-uat.trinasolar.com/ipaassub/ipaassub","manualUrl":"https://newipaas-mgr-uat.trinasolar.com/index","actuallyTime":1754841600000,"cluster":null,"relationSystem":null,"nodeSpec":null,"version":null,"appDomain":"mbtd","appName":"dp","productLine":"lc","bizUnit":"ess","bizScopeType":"ms","bizScope":"中国区","syncNextSystem":"cmdb","externalProductName":"","productVendor":"","implVendor":"","licenseMode":"","licenseDescription":null,"priorityLevel":null,"serviceHours":"","description":"测试导入1","flowDomain":"67","mobile":false}
2025-09-04 16:46:01.052 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | filteredBusinessData={}
2025-09-04 16:46:01.052 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=da
2025-09-04 16:46:01.052 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | rawBusinessData={"id":"1955437618749476866","createdTime":1755047633000,"creatorId":"1923297639180611586","creatorName":"刘顺","updatedTime":1755047633000,"updaterId":"1923297639180611586","updaterName":"刘顺","tenantId":1,"cnName":"测试0813-1","cnSimpleName":"测试","enName":"test0813-1","enSimpleName":"test","cloudName":null,"cloudAppId":null,"cloudProjectId":null,"cloudProjectName":null,"cloudProjectAliasName":null,"cloudTenantId":null,"cloudTenantName":null,"cloudTenantAliasName":null,"cloudNamespaceId":null,"cloudNamespaceName":null,"cloudClusterId":null,"stageStatus":"Planning","lockFlag":"0","appUrl":null,"remark":"系统备注222","detailPicUrl":null,"logoUrl":"","titleType":null,"imgTitle":null,"title":null,"subTitle":null,"bgImage":null,"showType":null,"alignment":null,"appStyle":null,"appType":null,"isOuter":null,"expireTime":null,"urlType":null,"configItems":null,"homePage":null,"showHomepage":0,"manageUrl":null,"isMobile":0,"isManagePcAppMenu":0,"contraction":0,"platformEnName":null,"platformCnName":null,"ieSupported":0,"ieTip":"null","isControlFP":null,"isControlDP":null,"isContainerApp":null,"tenantShow":null,"businessDomain":"ltc","tenantGroup":null,"isChangeTenant":null,"appSysType":"1,2","appGroup":"dmg","appServiceLevel":"3","constructionType":"Procurement","devLanguage":"lang_dotnet,lang_python","teamName":"ltc","gitlab":false,"devops":false,"apm":false,"code":"test","accessUrl":null,"manualUrl":null,"actuallyTime":null,"cluster":null,"relationSystem":null,"nodeSpec":null,"version":"v0.6","appDomain":"mbtd","appName":"dp","productLine":"tp","bizUnit":"ess","bizScopeType":"ms","bizScope":"apac,na,latam","syncNextSystem":"devops,gitlab","externalProductName":"TASP","productVendor":"xykj","implVendor":"xykj","licenseMode":"auth_annual","licenseDescription":"aaaaaaa","priorityLevel":null,"serviceHours":"8*20天","description":"系统定位描述111","flowDomain":"测试","mobile":false}
2025-09-04 16:46:01.052 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | filteredBusinessData={}
2025-09-04 16:46:01.052 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=test
2025-09-04 16:46:01.052 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | rawBusinessData={"id":"1955454171852795906","createdTime":1755051580000,"creatorId":"1923297639180611586","creatorName":"刘顺","updatedTime":1755051580000,"updaterId":"1923297639180611586","updaterName":"刘顺","tenantId":1,"cnName":"测试813-51","cnSimpleName":"测试813-51","enName":"test813-51","enSimpleName":"test813-51","cloudName":null,"cloudAppId":null,"cloudProjectId":null,"cloudProjectName":null,"cloudProjectAliasName":null,"cloudTenantId":null,"cloudTenantName":null,"cloudTenantAliasName":null,"cloudNamespaceId":null,"cloudNamespaceName":null,"cloudClusterId":null,"stageStatus":"Constructing","lockFlag":"0","appUrl":null,"remark":null,"detailPicUrl":null,"logoUrl":"","titleType":null,"imgTitle":null,"title":null,"subTitle":null,"bgImage":null,"showType":null,"alignment":null,"appStyle":null,"appType":null,"isOuter":null,"expireTime":null,"urlType":null,"configItems":null,"homePage":null,"showHomepage":0,"manageUrl":null,"isMobile":0,"isManagePcAppMenu":0,"contraction":0,"platformEnName":null,"platformCnName":null,"ieSupported":0,"ieTip":"null","isControlFP":null,"isControlDP":null,"isContainerApp":null,"tenantShow":null,"businessDomain":"tasptest","tenantGroup":null,"isChangeTenant":null,"appSysType":"1,2","appGroup":"dmg","appServiceLevel":"3","constructionType":"SelfDevelopment","devLanguage":"lang_java,lang_js","teamName":"tasptest","gitlab":false,"devops":false,"apm":false,"code":"test813-51","accessUrl":null,"manualUrl":null,"actuallyTime":null,"cluster":null,"relationSystem":null,"nodeSpec":null,"version":null,"appDomain":"mbtd","appName":"dp","productLine":"tp","bizUnit":"ess","bizScopeType":"ms","bizScope":"cn","syncNextSystem":"devops,gitlab","externalProductName":null,"productVendor":null,"implVendor":null,"licenseMode":null,"licenseDescription":null,"priorityLevel":null,"serviceHours":null,"description":null,"flowDomain":null,"mobile":false}
2025-09-04 16:46:01.052 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | filteredBusinessData={}
2025-09-04 16:46:01.052 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=test813-51
2025-09-04 16:46:01.053 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | rawBusinessData={"id":"1955458253531344897","createdTime":1755052553000,"creatorId":"1923297639180611586","creatorName":"刘顺","updatedTime":1755052553000,"updaterId":"1923297639180611586","updaterName":"刘顺","tenantId":1,"cnName":"测试813-52","cnSimpleName":"测试813-52","enName":"test813-52","enSimpleName":"test813-52","cloudName":null,"cloudAppId":null,"cloudProjectId":null,"cloudProjectName":null,"cloudProjectAliasName":null,"cloudTenantId":null,"cloudTenantName":null,"cloudTenantAliasName":null,"cloudNamespaceId":null,"cloudNamespaceName":null,"cloudClusterId":null,"stageStatus":"Constructing","lockFlag":"0","appUrl":null,"remark":null,"detailPicUrl":null,"logoUrl":"","titleType":null,"imgTitle":null,"title":null,"subTitle":null,"bgImage":null,"showType":null,"alignment":null,"appStyle":null,"appType":null,"isOuter":null,"expireTime":null,"urlType":null,"configItems":null,"homePage":null,"showHomepage":0,"manageUrl":null,"isMobile":0,"isManagePcAppMenu":0,"contraction":0,"platformEnName":null,"platformCnName":null,"ieSupported":0,"ieTip":"null","isControlFP":null,"isControlDP":null,"isContainerApp":null,"tenantShow":null,"businessDomain":"ltc","tenantGroup":null,"isChangeTenant":null,"appSysType":"1,2","appGroup":"apag","appServiceLevel":"3","constructionType":"SelfDevelopment","devLanguage":"lang_java,lang_js","teamName":"ltc","gitlab":false,"devops":false,"apm":false,"code":"test813-52","accessUrl":null,"manualUrl":null,"actuallyTime":null,"cluster":null,"relationSystem":null,"nodeSpec":null,"version":null,"appDomain":"mbtd","appName":"apa","productLine":"tp","bizUnit":"ess","bizScopeType":"ms","bizScope":"cn","syncNextSystem":"devops,gitlab","externalProductName":null,"productVendor":null,"implVendor":null,"licenseMode":null,"licenseDescription":null,"priorityLevel":null,"serviceHours":null,"description":null,"flowDomain":null,"mobile":false}
2025-09-04 16:46:01.053 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | filteredBusinessData={}
2025-09-04 16:46:01.053 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=test813-52
2025-09-04 16:46:01.053 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | rawBusinessData={"id":"1955458694021345282","createdTime":1755052658000,"creatorId":"1923297639180611586","creatorName":"刘顺","updatedTime":1755052658000,"updaterId":"1923297639180611586","updaterName":"刘顺","tenantId":1,"cnName":"测试813-2","cnSimpleName":"测试","enName":"test813-2","enSimpleName":"test","cloudName":null,"cloudAppId":null,"cloudProjectId":null,"cloudProjectName":null,"cloudProjectAliasName":null,"cloudTenantId":null,"cloudTenantName":null,"cloudTenantAliasName":null,"cloudNamespaceId":null,"cloudNamespaceName":null,"cloudClusterId":null,"stageStatus":"Planning","lockFlag":"0","appUrl":null,"remark":null,"detailPicUrl":null,"logoUrl":"","titleType":null,"imgTitle":null,"title":null,"subTitle":null,"bgImage":null,"showType":null,"alignment":null,"appStyle":null,"appType":null,"isOuter":null,"expireTime":null,"urlType":null,"configItems":null,"homePage":null,"showHomepage":0,"manageUrl":null,"isMobile":0,"isManagePcAppMenu":0,"contraction":0,"platformEnName":null,"platformCnName":null,"ieSupported":0,"ieTip":"null","isControlFP":null,"isControlDP":null,"isContainerApp":null,"tenantShow":null,"businessDomain":"tasptest","tenantGroup":null,"isChangeTenant":null,"appSysType":"3,2","appGroup":"dmg","appServiceLevel":"3","constructionType":"Procurement","devLanguage":"lang_java,lang_dotnet","teamName":"tasptest","gitlab":false,"devops":false,"apm":false,"code":"test","accessUrl":null,"manualUrl":null,"actuallyTime":null,"cluster":null,"relationSystem":null,"nodeSpec":null,"version":null,"appDomain":"mbtd","appName":"dp","productLine":"tp","bizUnit":"ess","bizScopeType":"ms","bizScope":"us,eu,mea","syncNextSystem":"devops,gitlab","externalProductName":"tasp","productVendor":"xykj","implVendor":"xykj","licenseMode":"auth_annual","licenseDescription":null,"priorityLevel":null,"serviceHours":null,"description":null,"flowDomain":null,"mobile":false}
2025-09-04 16:46:01.053 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | filteredBusinessData={}
2025-09-04 16:46:01.053 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=test
2025-09-04 16:46:01.053 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | rawBusinessData={"id":"1955461941180198913","createdTime":1755053432000,"creatorId":"1923297639180611586","creatorName":"刘顺","updatedTime":1755053432000,"updaterId":"1923297639180611586","updaterName":"刘顺","tenantId":1,"cnName":"测试813-4","cnSimpleName":"测试","enName":"test813-4","enSimpleName":"test","cloudName":null,"cloudAppId":null,"cloudProjectId":null,"cloudProjectName":null,"cloudProjectAliasName":null,"cloudTenantId":null,"cloudTenantName":null,"cloudTenantAliasName":null,"cloudNamespaceId":null,"cloudNamespaceName":null,"cloudClusterId":null,"stageStatus":"Planning","lockFlag":"0","appUrl":null,"remark":null,"detailPicUrl":null,"logoUrl":"","titleType":null,"imgTitle":null,"title":null,"subTitle":null,"bgImage":null,"showType":null,"alignment":null,"appStyle":null,"appType":null,"isOuter":null,"expireTime":null,"urlType":null,"configItems":null,"homePage":null,"showHomepage":0,"manageUrl":null,"isMobile":0,"isManagePcAppMenu":0,"contraction":0,"platformEnName":null,"platformCnName":null,"ieSupported":0,"ieTip":"null","isControlFP":null,"isControlDP":null,"isContainerApp":null,"tenantShow":null,"businessDomain":"ipd","tenantGroup":null,"isChangeTenant":null,"appSysType":"2,1","appGroup":"dmg","appServiceLevel":"3","constructionType":"SelfDevelopment","devLanguage":"lang_dotnet","teamName":"ipd","gitlab":false,"devops":false,"apm":false,"code":"test","accessUrl":null,"manualUrl":null,"actuallyTime":null,"cluster":null,"relationSystem":null,"nodeSpec":null,"version":null,"appDomain":"mbtd","appName":"dp","productLine":"lc","bizUnit":"back","bizScopeType":"im","bizScope":"vn_jd,us_jd","syncNextSystem":"devops,gitlab","externalProductName":null,"productVendor":null,"implVendor":null,"licenseMode":null,"licenseDescription":null,"priorityLevel":null,"serviceHours":null,"description":null,"flowDomain":null,"mobile":false}
2025-09-04 16:46:01.053 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | filteredBusinessData={}
2025-09-04 16:46:01.054 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=test
2025-09-04 16:46:01.054 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | rawBusinessData={"id":"1955472011757064194","createdTime":1755055833000,"creatorId":"1923294602840059905","creatorName":"Yang_Gang TS/TPD(杨刚)","updatedTime":1755055833000,"updaterId":"1923294602840059905","updaterName":"Yang_Gang TS/TPD(杨刚)","tenantId":1,"cnName":"这是一个应用系统","cnSimpleName":"试试","enName":"zsygyyxt","enSimpleName":"ss","cloudName":null,"cloudAppId":null,"cloudProjectId":null,"cloudProjectName":null,"cloudProjectAliasName":null,"cloudTenantId":null,"cloudTenantName":null,"cloudTenantAliasName":null,"cloudNamespaceId":null,"cloudNamespaceName":null,"cloudClusterId":null,"stageStatus":"Constructing","lockFlag":"0","appUrl":null,"remark":"","detailPicUrl":null,"logoUrl":"","titleType":null,"imgTitle":null,"title":null,"subTitle":null,"bgImage":null,"showType":null,"alignment":null,"appStyle":null,"appType":null,"isOuter":null,"expireTime":null,"urlType":null,"configItems":null,"homePage":null,"showHomepage":0,"manageUrl":null,"isMobile":0,"isManagePcAppMenu":0,"contraction":0,"platformEnName":null,"platformCnName":null,"ieSupported":0,"ieTip":"null","isControlFP":null,"isControlDP":null,"isContainerApp":null,"tenantShow":null,"businessDomain":"ipd","tenantGroup":null,"isChangeTenant":null,"appSysType":"1,2,3","appGroup":"iitg","appServiceLevel":"3","constructionType":"Procurement","devLanguage":"lang_java,lang_dotnet","teamName":"ipd","gitlab":false,"devops":false,"apm":false,"code":"123","accessUrl":null,"manualUrl":null,"actuallyTime":null,"cluster":null,"relationSystem":null,"nodeSpec":null,"version":null,"appDomain":"mbtd","appName":"ehs","productLine":"aip","bizUnit":"gen","bizScopeType":"other","bizScope":"cn_hq","syncNextSystem":"devops,gitlab","externalProductName":"光伏板","productVendor":"谐云科技","implVendor":"天合光能","licenseMode":"auth_multi","licenseDescription":null,"priorityLevel":null,"serviceHours":null,"description":null,"flowDomain":null,"mobile":false}
2025-09-04 16:46:01.054 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | filteredBusinessData={}
2025-09-04 16:46:01.054 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=123
2025-09-04 16:46:01.054 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | rawBusinessData={"id":"1955799947956420609","createdTime":1755134019000,"creatorId":"1923297639180611586","creatorName":"刘顺","updatedTime":1755134019000,"updaterId":"1923297639180611586","updaterName":"刘顺","tenantId":1,"cnName":"测试814-1","cnSimpleName":"测试814-1","enName":"test814-1","enSimpleName":"test","cloudName":null,"cloudAppId":null,"cloudProjectId":null,"cloudProjectName":null,"cloudProjectAliasName":null,"cloudTenantId":null,"cloudTenantName":null,"cloudTenantAliasName":null,"cloudNamespaceId":null,"cloudNamespaceName":null,"cloudClusterId":null,"stageStatus":"Constructing","lockFlag":"0","appUrl":null,"remark":null,"detailPicUrl":null,"logoUrl":"","titleType":null,"imgTitle":null,"title":null,"subTitle":null,"bgImage":null,"showType":null,"alignment":null,"appStyle":null,"appType":null,"isOuter":null,"expireTime":null,"urlType":null,"configItems":null,"homePage":null,"showHomepage":0,"manageUrl":null,"isMobile":0,"isManagePcAppMenu":0,"contraction":0,"platformEnName":null,"platformCnName":null,"ieSupported":0,"ieTip":"null","isControlFP":null,"isControlDP":null,"isContainerApp":null,"tenantShow":null,"businessDomain":"isc","tenantGroup":null,"isChangeTenant":null,"appSysType":"1,2","appGroup":"apag","appServiceLevel":"3","constructionType":"SelfDevelopment","devLanguage":"lang_java,lang_js","teamName":"isc","gitlab":false,"devops":false,"apm":false,"code":"test814-1","accessUrl":null,"manualUrl":null,"actuallyTime":null,"cluster":null,"relationSystem":null,"nodeSpec":null,"version":null,"appDomain":"mbtd","appName":"apa","productLine":"dp","bizUnit":"ess","bizScopeType":"im","bizScope":"cn_jd","syncNextSystem":"devops,gitlab","externalProductName":null,"productVendor":null,"implVendor":null,"licenseMode":null,"licenseDescription":null,"priorityLevel":null,"serviceHours":null,"description":null,"flowDomain":null,"mobile":false}
2025-09-04 16:46:01.054 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | filteredBusinessData={}
2025-09-04 16:46:01.054 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=test814-1
2025-09-04 16:46:01.054 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | rawBusinessData={"id":"1955826041712095234","createdTime":1755140240000,"creatorId":"1923297639180611586","creatorName":"刘顺","updatedTime":1755140240000,"updaterId":"1923297639180611586","updaterName":"刘顺","tenantId":1,"cnName":"测试814-2","cnSimpleName":"测试2","enName":"test814-2","enSimpleName":"t8141007","cloudName":null,"cloudAppId":null,"cloudProjectId":null,"cloudProjectName":null,"cloudProjectAliasName":null,"cloudTenantId":null,"cloudTenantName":null,"cloudTenantAliasName":null,"cloudNamespaceId":null,"cloudNamespaceName":null,"cloudClusterId":null,"stageStatus":null,"lockFlag":"0","appUrl":null,"remark":"测试-备注","detailPicUrl":null,"logoUrl":null,"titleType":null,"imgTitle":null,"title":null,"subTitle":null,"bgImage":null,"showType":null,"alignment":null,"appStyle":null,"appType":null,"isOuter":null,"expireTime":null,"urlType":null,"configItems":null,"homePage":null,"showHomepage":0,"manageUrl":null,"isMobile":0,"isManagePcAppMenu":0,"contraction":0,"platformEnName":null,"platformCnName":null,"ieSupported":0,"ieTip":"null","isControlFP":null,"isControlDP":null,"isContainerApp":null,"tenantShow":null,"businessDomain":"tasptest","tenantGroup":null,"isChangeTenant":null,"appSysType":"6,7","appGroup":"isg","appServiceLevel":"3","constructionType":"SelfDevelopment","devLanguage":"lang_java,lang_dotnet","teamName":null,"gitlab":false,"devops":false,"apm":false,"code":"testcase","accessUrl":"https://tasp-test.trinasolar.com/","manualUrl":"https://tasp-dev.trinasolar.com/","actuallyTime":1741017600000,"cluster":null,"relationSystem":null,"nodeSpec":null,"version":null,"appDomain":"mbtd","appName":"isa","productLine":"dp","bizUnit":"sol","bizScopeType":"ms","bizScope":"null,null","syncNextSystem":"","externalProductName":"","productVendor":"","implVendor":"","licenseMode":"","licenseDescription":"","priorityLevel":null,"serviceHours":"5*8小时服务","description":"测试-系统定位描述","flowDomain":"6","mobile":false}
2025-09-04 16:46:01.054 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | filteredBusinessData={}
2025-09-04 16:46:01.054 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=testcase
2025-09-04 16:46:01.055 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | rawBusinessData={"id":"1955830895503544321","createdTime":1755141398000,"creatorId":"1923297639180611586","creatorName":"刘顺","updatedTime":1755141398000,"updaterId":"1923297639180611586","updaterName":"刘顺","tenantId":1,"cnName":"测试814-52","cnSimpleName":"测试52","enName":"test814-52","enSimpleName":"t81410052","cloudName":null,"cloudAppId":null,"cloudProjectId":null,"cloudProjectName":null,"cloudProjectAliasName":null,"cloudTenantId":null,"cloudTenantName":null,"cloudTenantAliasName":null,"cloudNamespaceId":null,"cloudNamespaceName":null,"cloudClusterId":null,"stageStatus":null,"lockFlag":"0","appUrl":null,"remark":"测试-备注","detailPicUrl":null,"logoUrl":null,"titleType":null,"imgTitle":null,"title":null,"subTitle":null,"bgImage":null,"showType":null,"alignment":null,"appStyle":null,"appType":null,"isOuter":null,"expireTime":null,"urlType":null,"configItems":null,"homePage":null,"showHomepage":0,"manageUrl":null,"isMobile":0,"isManagePcAppMenu":0,"contraction":0,"platformEnName":null,"platformCnName":null,"ieSupported":0,"ieTip":"null","isControlFP":null,"isControlDP":null,"isContainerApp":null,"tenantShow":null,"businessDomain":"tasptest","tenantGroup":null,"isChangeTenant":null,"appSysType":"6,7","appGroup":"isg","appServiceLevel":"3","constructionType":"SelfDevelopment","devLanguage":"lang_java,lang_dotnet","teamName":null,"gitlab":false,"devops":false,"apm":false,"code":"testcase","accessUrl":"https://tasp-test.trinasolar.com/","manualUrl":"https://tasp-dev.trinasolar.com/","actuallyTime":1741017600000,"cluster":null,"relationSystem":null,"nodeSpec":null,"version":null,"appDomain":"mbtd","appName":"isa","productLine":"dp","bizUnit":"sol","bizScopeType":"ms","bizScope":"cn,us","syncNextSystem":"","externalProductName":"","productVendor":"","implVendor":"","licenseMode":"","licenseDescription":"","priorityLevel":null,"serviceHours":"5*8小时服务","description":"测试-系统定位描述","flowDomain":"6","mobile":false}
2025-09-04 16:46:01.055 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | filteredBusinessData={}
2025-09-04 16:46:01.055 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=testcase
2025-09-04 16:46:01.055 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | rawBusinessData={"id":"1955881432294064130","createdTime":1755153447000,"creatorId":"1923297639180611586","creatorName":"刘顺","updatedTime":1755153447000,"updaterId":"1923297639180611586","updaterName":"刘顺","tenantId":1,"cnName":"测试814-8","cnSimpleName":"测试8","enName":"test814-8","enSimpleName":"t8141348","cloudName":null,"cloudAppId":null,"cloudProjectId":null,"cloudProjectName":null,"cloudProjectAliasName":null,"cloudTenantId":null,"cloudTenantName":null,"cloudTenantAliasName":null,"cloudNamespaceId":null,"cloudNamespaceName":null,"cloudClusterId":null,"stageStatus":"Planning","lockFlag":"0","appUrl":null,"remark":"测试-备注","detailPicUrl":null,"logoUrl":null,"titleType":null,"imgTitle":null,"title":null,"subTitle":null,"bgImage":null,"showType":null,"alignment":null,"appStyle":null,"appType":null,"isOuter":null,"expireTime":null,"urlType":null,"configItems":null,"homePage":null,"showHomepage":0,"manageUrl":null,"isMobile":0,"isManagePcAppMenu":0,"contraction":0,"platformEnName":null,"platformCnName":null,"ieSupported":0,"ieTip":"null","isControlFP":null,"isControlDP":null,"isContainerApp":null,"tenantShow":null,"businessDomain":"tasptest","tenantGroup":null,"isChangeTenant":null,"appSysType":"6,7","appGroup":"isg","appServiceLevel":"3","constructionType":"SelfDevelopment","devLanguage":"lang_java,lang_dotnet","teamName":null,"gitlab":false,"devops":false,"apm":false,"code":"testcase","accessUrl":"https://tasp-test.trinasolar.com/","manualUrl":"https://tasp-dev.trinasolar.com/","actuallyTime":1741017600000,"cluster":null,"relationSystem":null,"nodeSpec":null,"version":null,"appDomain":"mbtd","appName":"isa","productLine":"dp","bizUnit":"sol","bizScopeType":"ms","bizScope":"cn,us","syncNextSystem":"devops,gitlab","externalProductName":"","productVendor":"","implVendor":"","licenseMode":"","licenseDescription":"","priorityLevel":null,"serviceHours":"5*8小时服务","description":"测试-系统定位描述","flowDomain":"6","mobile":false}
2025-09-04 16:46:01.055 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | filteredBusinessData={}
2025-09-04 16:46:01.055 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=testcase
2025-09-04 16:46:01.055 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | rawBusinessData={"id":"1955886360122306561","createdTime":1755154622000,"creatorId":"1923297639180611586","creatorName":"刘顺","updatedTime":1755154622000,"updaterId":"1923297639180611586","updaterName":"刘顺","tenantId":1,"cnName":"测试d814-9","cnSimpleName":"测试d9","enName":"testd814-9","enSimpleName":"d8141349","cloudName":null,"cloudAppId":null,"cloudProjectId":null,"cloudProjectName":null,"cloudProjectAliasName":null,"cloudTenantId":null,"cloudTenantName":null,"cloudTenantAliasName":null,"cloudNamespaceId":null,"cloudNamespaceName":null,"cloudClusterId":null,"stageStatus":"Planning","lockFlag":"0","appUrl":null,"remark":"测试-备注","detailPicUrl":null,"logoUrl":null,"titleType":null,"imgTitle":null,"title":null,"subTitle":null,"bgImage":null,"showType":null,"alignment":null,"appStyle":null,"appType":null,"isOuter":null,"expireTime":null,"urlType":null,"configItems":null,"homePage":null,"showHomepage":0,"manageUrl":null,"isMobile":0,"isManagePcAppMenu":0,"contraction":0,"platformEnName":null,"platformCnName":null,"ieSupported":0,"ieTip":"null","isControlFP":null,"isControlDP":null,"isContainerApp":null,"tenantShow":null,"businessDomain":"tasptest","tenantGroup":null,"isChangeTenant":null,"appSysType":"6,7","appGroup":"isg","appServiceLevel":"3","constructionType":"SelfDevelopment","devLanguage":"lang_java,lang_dotnet","teamName":null,"gitlab":false,"devops":false,"apm":false,"code":"testcase","accessUrl":"https://tasp-test.trinasolar.com/","manualUrl":"https://tasp-dev.trinasolar.com/","actuallyTime":1741017600000,"cluster":null,"relationSystem":null,"nodeSpec":null,"version":null,"appDomain":"mbtd","appName":"isa","productLine":"dp","bizUnit":"sol","bizScopeType":"ms","bizScope":"cn,us","syncNextSystem":"devops,gitlab","externalProductName":"","productVendor":"","implVendor":"","licenseMode":"","licenseDescription":"","priorityLevel":null,"serviceHours":"5*8小时服务","description":"测试-系统定位描述","flowDomain":"6","mobile":false}
2025-09-04 16:46:01.055 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | filteredBusinessData={}
2025-09-04 16:46:01.055 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=testcase
2025-09-04 16:46:01.056 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | rawBusinessData={"id":"1957272010127007745","createdTime":1755484986000,"creatorId":"1923296746045517826","creatorName":"彭书友","updatedTime":1755484986000,"updaterId":"1923296746045517826","updaterName":"彭书友","tenantId":1,"cnName":"测试182","cnSimpleName":"测试182","enName":"test182","enSimpleName":"test182","cloudName":null,"cloudAppId":null,"cloudProjectId":null,"cloudProjectName":null,"cloudProjectAliasName":null,"cloudTenantId":null,"cloudTenantName":null,"cloudTenantAliasName":null,"cloudNamespaceId":null,"cloudNamespaceName":null,"cloudClusterId":null,"stageStatus":"Constructing","lockFlag":"0","appUrl":null,"remark":null,"detailPicUrl":null,"logoUrl":"","titleType":null,"imgTitle":null,"title":null,"subTitle":null,"bgImage":null,"showType":null,"alignment":null,"appStyle":null,"appType":null,"isOuter":null,"expireTime":null,"urlType":null,"configItems":null,"homePage":null,"showHomepage":0,"manageUrl":null,"isMobile":0,"isManagePcAppMenu":0,"contraction":0,"platformEnName":null,"platformCnName":null,"ieSupported":0,"ieTip":"null","isControlFP":null,"isControlDP":null,"isContainerApp":null,"tenantShow":null,"businessDomain":"itid","tenantGroup":null,"isChangeTenant":null,"appSysType":"1,2","appGroup":"dmg","appServiceLevel":"3","constructionType":"SelfDevelopment","devLanguage":"lang_java,lang_js","teamName":"itid","gitlab":false,"devops":false,"apm":false,"code":"test182","accessUrl":null,"manualUrl":null,"actuallyTime":null,"cluster":null,"relationSystem":null,"nodeSpec":null,"version":null,"appDomain":"mbtd","appName":"dp","productLine":"lc","bizUnit":"ess","bizScopeType":"ms","bizScope":"cn","syncNextSystem":"devops,gitlab","externalProductName":null,"productVendor":null,"implVendor":null,"licenseMode":null,"licenseDescription":null,"priorityLevel":null,"serviceHours":null,"description":null,"flowDomain":"2","mobile":false}
2025-09-04 16:46:01.056 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | filteredBusinessData={}
2025-09-04 16:46:01.056 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=test182
2025-09-04 16:46:01.056 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | rawBusinessData={"id":"1957275516464488449","createdTime":1755485822000,"creatorId":"1923296746045517826","creatorName":"彭书友","updatedTime":1755485822000,"updaterId":"1923296746045517826","updaterName":"彭书友","tenantId":1,"cnName":"测试系统183","cnSimpleName":"测试系统183","enName":"test183","enSimpleName":"test183","cloudName":null,"cloudAppId":null,"cloudProjectId":null,"cloudProjectName":null,"cloudProjectAliasName":null,"cloudTenantId":null,"cloudTenantName":null,"cloudTenantAliasName":null,"cloudNamespaceId":null,"cloudNamespaceName":null,"cloudClusterId":null,"stageStatus":"Constructing","lockFlag":"0","appUrl":null,"remark":null,"detailPicUrl":null,"logoUrl":"","titleType":null,"imgTitle":null,"title":null,"subTitle":null,"bgImage":null,"showType":null,"alignment":null,"appStyle":null,"appType":null,"isOuter":null,"expireTime":null,"urlType":null,"configItems":null,"homePage":null,"showHomepage":0,"manageUrl":null,"isMobile":0,"isManagePcAppMenu":0,"contraction":0,"platformEnName":null,"platformCnName":null,"ieSupported":0,"ieTip":"null","isControlFP":null,"isControlDP":null,"isContainerApp":null,"tenantShow":null,"businessDomain":"itid","tenantGroup":null,"isChangeTenant":null,"appSysType":"1,2","appGroup":"pmg","appServiceLevel":"3","constructionType":"SelfDevelopment","devLanguage":"lang_java,lang_swift","teamName":"itid","gitlab":false,"devops":false,"apm":false,"code":"test183","accessUrl":null,"manualUrl":null,"actuallyTime":null,"cluster":null,"relationSystem":null,"nodeSpec":null,"version":null,"appDomain":"mbtd","appName":"pma","productLine":"lc","bizUnit":"ess","bizScopeType":"ms","bizScope":"cn","syncNextSystem":"devops,gitlab","externalProductName":null,"productVendor":null,"implVendor":null,"licenseMode":null,"licenseDescription":null,"priorityLevel":null,"serviceHours":null,"description":null,"flowDomain":"3","mobile":false}
2025-09-04 16:46:01.056 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | filteredBusinessData={}
2025-09-04 16:46:01.056 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=test183
2025-09-04 16:46:01.057 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | rawBusinessData={"id":"1957998824919097345","createdTime":1755658272000,"creatorId":"1923294602840059905","creatorName":"Yang_Gang TS/TPD(杨刚)","updatedTime":1755658272000,"updaterId":"1923294602840059905","updaterName":"Yang_Gang TS/TPD(杨刚)","tenantId":1,"cnName":"水电费水电费","cnSimpleName":"是的发烧的方式的","enName":"sdfsadfsadf","enSimpleName":"sdfsdfsdf","cloudName":null,"cloudAppId":null,"cloudProjectId":null,"cloudProjectName":null,"cloudProjectAliasName":null,"cloudTenantId":null,"cloudTenantName":null,"cloudTenantAliasName":null,"cloudNamespaceId":null,"cloudNamespaceName":null,"cloudClusterId":null,"stageStatus":"Constructing","lockFlag":"0","appUrl":null,"remark":"werwe","detailPicUrl":null,"logoUrl":"","titleType":null,"imgTitle":null,"title":null,"subTitle":null,"bgImage":null,"showType":null,"alignment":null,"appStyle":null,"appType":null,"isOuter":null,"expireTime":null,"urlType":null,"configItems":null,"homePage":null,"showHomepage":0,"manageUrl":null,"isMobile":0,"isManagePcAppMenu":0,"contraction":0,"platformEnName":null,"platformCnName":null,"ieSupported":0,"ieTip":"null","isControlFP":null,"isControlDP":null,"isContainerApp":null,"tenantShow":null,"businessDomain":"ltc","tenantGroup":null,"isChangeTenant":null,"appSysType":"1","appGroup":"apag","appServiceLevel":"1","constructionType":"SelfDevelopment","devLanguage":"lang_java","teamName":"ltc","gitlab":false,"devops":false,"apm":false,"code":"sdfsdf","accessUrl":"werewr","manualUrl":null,"actuallyTime":null,"cluster":null,"relationSystem":null,"nodeSpec":null,"version":"werew","appDomain":"mbtd","appName":"apa","productLine":"aip","bizUnit":"ess","bizScopeType":"ms","bizScope":"cn,us","syncNextSystem":"devops,gitlab","externalProductName":null,"productVendor":null,"implVendor":null,"licenseMode":null,"licenseDescription":null,"priorityLevel":null,"serviceHours":"werwe","description":"werew","flowDomain":"5.0","mobile":false}
2025-09-04 16:46:01.057 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | filteredBusinessData={}
2025-09-04 16:46:01.057 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=sdfsdf
2025-09-04 16:46:01.057 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | rawBusinessData={"id":"1963126949253324801","createdTime":1756880912000,"creatorId":"1923297639180611586","creatorName":"刘顺","updatedTime":1756880912000,"updaterId":"1923297639180611586","updaterName":"刘顺","tenantId":1,"cnName":"测试系统903","cnSimpleName":"测试系统903","enName":"test903","enSimpleName":"test903","cloudName":null,"cloudAppId":null,"cloudProjectId":null,"cloudProjectName":null,"cloudProjectAliasName":null,"cloudTenantId":null,"cloudTenantName":null,"cloudTenantAliasName":null,"cloudNamespaceId":null,"cloudNamespaceName":null,"cloudClusterId":null,"stageStatus":"Constructing","lockFlag":"0","appUrl":null,"remark":null,"detailPicUrl":null,"logoUrl":"","titleType":null,"imgTitle":null,"title":null,"subTitle":null,"bgImage":null,"showType":null,"alignment":null,"appStyle":null,"appType":null,"isOuter":null,"expireTime":null,"urlType":null,"configItems":null,"homePage":null,"showHomepage":0,"manageUrl":null,"isMobile":0,"isManagePcAppMenu":0,"contraction":0,"platformEnName":null,"platformCnName":null,"ieSupported":0,"ieTip":"null","isControlFP":null,"isControlDP":null,"isContainerApp":null,"tenantShow":null,"businessDomain":"tasptest","tenantGroup":null,"isChangeTenant":null,"appSysType":"1","appGroup":"apag","appServiceLevel":"3","constructionType":"SelfDevelopment","devLanguage":"lang_java,lang_swift","teamName":"tasptest","gitlab":false,"devops":false,"apm":false,"code":"test903","accessUrl":null,"manualUrl":null,"actuallyTime":null,"cluster":null,"relationSystem":null,"nodeSpec":null,"version":null,"appDomain":"mbtd","appName":"apa","productLine":"lc","bizUnit":"gen","bizScopeType":"ms","bizScope":"cn","syncNextSystem":"devops,gitlab,sca","externalProductName":null,"productVendor":null,"implVendor":null,"licenseMode":null,"licenseDescription":null,"priorityLevel":null,"serviceHours":"5*8小时服务","description":null,"flowDomain":"3.0","mobile":false}
2025-09-04 16:46:01.057 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | filteredBusinessData={}
2025-09-04 16:46:01.057 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=test903
2025-09-04 16:46:01.057 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | rawBusinessData={"id":"1963146155931836417","createdTime":1756885492000,"creatorId":"1923297639180611586","creatorName":"刘顺","updatedTime":1756885492000,"updaterId":"1923297639180611586","updaterName":"刘顺","tenantId":1,"cnName":"测试系统9031","cnSimpleName":"测试系统9031","enName":"test9031","enSimpleName":"test9031","cloudName":null,"cloudAppId":null,"cloudProjectId":null,"cloudProjectName":null,"cloudProjectAliasName":null,"cloudTenantId":null,"cloudTenantName":null,"cloudTenantAliasName":null,"cloudNamespaceId":null,"cloudNamespaceName":null,"cloudClusterId":null,"stageStatus":"Constructing","lockFlag":"0","appUrl":null,"remark":null,"detailPicUrl":null,"logoUrl":"","titleType":null,"imgTitle":null,"title":null,"subTitle":null,"bgImage":null,"showType":null,"alignment":null,"appStyle":null,"appType":null,"isOuter":null,"expireTime":null,"urlType":null,"configItems":null,"homePage":null,"showHomepage":0,"manageUrl":null,"isMobile":0,"isManagePcAppMenu":0,"contraction":0,"platformEnName":null,"platformCnName":null,"ieSupported":0,"ieTip":"null","isControlFP":null,"isControlDP":null,"isContainerApp":null,"tenantShow":null,"businessDomain":"tasptest","tenantGroup":null,"isChangeTenant":null,"appSysType":"1","appGroup":"apag","appServiceLevel":"3","constructionType":"SelfDevelopment","devLanguage":"lang_java,lang_swift","teamName":"tasptest","gitlab":false,"devops":false,"apm":false,"code":"test9031","accessUrl":null,"manualUrl":null,"actuallyTime":null,"cluster":null,"relationSystem":null,"nodeSpec":null,"version":null,"appDomain":"mbtd","appName":"apa","productLine":"lc","bizUnit":"gen","bizScopeType":"ms","bizScope":"cn","syncNextSystem":"devops,gitlab,sca","externalProductName":null,"productVendor":null,"implVendor":null,"licenseMode":null,"licenseDescription":null,"priorityLevel":null,"serviceHours":"5*8小时服务","description":null,"flowDomain":"3.0","mobile":false}
2025-09-04 16:46:01.058 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | filteredBusinessData={}
2025-09-04 16:46:01.058 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=test9031
2025-09-04 16:46:01.058 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | rawBusinessData={"id":"1963476204224618497","createdTime":1756964181000,"creatorId":"1923297639180611586","creatorName":"刘顺","updatedTime":1756964181000,"updaterId":"1923297639180611586","updaterName":"刘顺","tenantId":1,"cnName":"测试系统-共享1","cnSimpleName":"测试系统-共享1","enName":"test-share1","enSimpleName":"testshare1","cloudName":null,"cloudAppId":null,"cloudProjectId":null,"cloudProjectName":null,"cloudProjectAliasName":null,"cloudTenantId":null,"cloudTenantName":null,"cloudTenantAliasName":null,"cloudNamespaceId":null,"cloudNamespaceName":null,"cloudClusterId":null,"stageStatus":"Constructing","lockFlag":"0","appUrl":null,"remark":null,"detailPicUrl":null,"logoUrl":"","titleType":null,"imgTitle":null,"title":null,"subTitle":null,"bgImage":null,"showType":null,"alignment":null,"appStyle":null,"appType":null,"isOuter":null,"expireTime":null,"urlType":null,"configItems":null,"homePage":null,"showHomepage":0,"manageUrl":null,"isMobile":0,"isManagePcAppMenu":0,"contraction":0,"platformEnName":null,"platformCnName":null,"ieSupported":0,"ieTip":"null","isControlFP":null,"isControlDP":null,"isContainerApp":null,"tenantShow":null,"businessDomain":"isc","tenantGroup":null,"isChangeTenant":null,"appSysType":"1","appGroup":"apag","appServiceLevel":"4","constructionType":"SelfDevelopment","devLanguage":"lang_java","teamName":"isc","gitlab":false,"devops":false,"apm":false,"code":"testshare1","accessUrl":null,"manualUrl":null,"actuallyTime":null,"cluster":null,"relationSystem":null,"nodeSpec":null,"version":null,"appDomain":"mbtd","appName":"apa","productLine":"aip","bizUnit":"ess","bizScopeType":"im","bizScope":"cn_jd","syncNextSystem":"devops,gitlab,sca","externalProductName":null,"productVendor":null,"implVendor":null,"licenseMode":null,"licenseDescription":null,"priorityLevel":null,"serviceHours":"5*8小时服务","description":null,"flowDomain":"1.0","mobile":false}
2025-09-04 16:46:01.058 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | filteredBusinessData={}
2025-09-04 16:46:01.058 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=testshare1
2025-09-04 16:46:01.058 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | rawBusinessData={"id":"1963501354846846977","createdTime":1756970178000,"creatorId":"1923297639180611586","creatorName":"刘顺","updatedTime":1756970178000,"updaterId":"1923297639180611586","updaterName":"刘顺","tenantId":1,"cnName":"共享测试2","cnSimpleName":"共享测试2","enName":"testshare2","enSimpleName":"testshare2","cloudName":null,"cloudAppId":null,"cloudProjectId":null,"cloudProjectName":null,"cloudProjectAliasName":null,"cloudTenantId":null,"cloudTenantName":null,"cloudTenantAliasName":null,"cloudNamespaceId":null,"cloudNamespaceName":null,"cloudClusterId":null,"stageStatus":"Constructing","lockFlag":"0","appUrl":null,"remark":null,"detailPicUrl":null,"logoUrl":"","titleType":null,"imgTitle":null,"title":null,"subTitle":null,"bgImage":null,"showType":null,"alignment":null,"appStyle":null,"appType":null,"isOuter":null,"expireTime":null,"urlType":null,"configItems":null,"homePage":null,"showHomepage":0,"manageUrl":null,"isMobile":0,"isManagePcAppMenu":0,"contraction":0,"platformEnName":null,"platformCnName":null,"ieSupported":0,"ieTip":"null","isControlFP":null,"isControlDP":null,"isContainerApp":null,"tenantShow":null,"businessDomain":"tasptest","tenantGroup":null,"isChangeTenant":null,"appSysType":"1","appGroup":"apag","appServiceLevel":"4","constructionType":"SelfDevelopment","devLanguage":"lang_java","teamName":"tasptest","gitlab":false,"devops":false,"apm":false,"code":"testshare2","accessUrl":null,"manualUrl":null,"actuallyTime":null,"cluster":null,"relationSystem":null,"nodeSpec":null,"version":null,"appDomain":"mbtd","appName":"apa","productLine":"aip","bizUnit":"ess","bizScopeType":"ms","bizScope":"cn","syncNextSystem":"devops,gitlab,sca","externalProductName":null,"productVendor":null,"implVendor":null,"licenseMode":null,"licenseDescription":null,"priorityLevel":null,"serviceHours":"5*8小时服务","description":"测试111","flowDomain":"2.0","mobile":false}
2025-09-04 16:46:01.058 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | filteredBusinessData={}
2025-09-04 16:46:01.058 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.service.impl.DataShareServiceImpl [0;39m | business_data为空，跳过推送消息到kafka，key=testshare2
2025-09-04 16:46:01.069 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.integration.task.DataShareSchedule  [0;39m | 执行应用系统全量共享任务结束
2025-09-04 16:47:00.018 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.task.ScaScanDistributedTask       [0;39m | =======执行的SCA状态扫描任务======
2025-09-04 16:47:00.119 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.task.ScaScanDistributedTask       [0;39m | []
2025-09-04 16:48:00.032 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.task.ScaScanDistributedTask       [0;39m | =======执行的SCA状态扫描任务======
2025-09-04 16:48:00.153 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.task.ScaScanDistributedTask       [0;39m | []
2025-09-04 16:49:00.019 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.task.ScaScanDistributedTask       [0;39m | =======执行的SCA状态扫描任务======
2025-09-04 16:49:00.126 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.task.ScaScanDistributedTask       [0;39m | []
2025-09-04 16:49:43.725 | [34m INFO 82922[0;39m | [1;33mhttp-nio-80-exec-3[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/kepler/integration/api/sca/comps/list/20040) 参数({pageNo=1, pattern=, pageSize=10, source=0, type=0, taskId=20040})]
2025-09-04 16:49:43.982 | [34m INFO 82922[0;39m | [1;33mhttp-nio-80-exec-3[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/kepler/integration/api/sca/comps/list/20040) 耗时(257 ms)]
2025-09-04 16:49:45.984 | [34m INFO 82922[0;39m | [1;33mhttp-nio-80-exec-4[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/kepler/integration/product/list) 无参数]
2025-09-04 16:49:46.065 | [34m INFO 82922[0;39m | [1;33mhttp-nio-80-exec-4[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/kepler/integration/product/list) 耗时(81 ms)]
2025-09-04 16:50:00.009 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.task.ScaScanDistributedTask       [0;39m | =======执行的SCA状态扫描任务======
2025-09-04 16:50:00.101 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.task.ScaScanDistributedTask       [0;39m | []
2025-09-04 16:50:58.669 | [34m INFO 82922[0;39m | [1;33mhttp-nio-80-exec-7[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/kepler/integration/api/sca/comps/list/20040) 参数({pageNo=1, pattern=, pageSize=10, source=0, type=0, taskId=20040})]
2025-09-04 16:50:58.895 | [34m INFO 82922[0;39m | [1;33mhttp-nio-80-exec-7[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/kepler/integration/api/sca/comps/list/20040) 耗时(225 ms)]
2025-09-04 16:51:00.014 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.task.ScaScanDistributedTask       [0;39m | =======执行的SCA状态扫描任务======
2025-09-04 16:51:00.080 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.task.ScaScanDistributedTask       [0;39m | []
2025-09-04 16:51:11.663 | [34m INFO 82922[0;39m | [1;33mhttp-nio-80-exec-8[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/kepler/integration/product/list) 参数({noCache=1756975877037})]
2025-09-04 16:51:11.723 | [34m INFO 82922[0;39m | [1;33mhttp-nio-80-exec-8[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/kepler/integration/product/list) 耗时(59 ms)]
2025-09-04 16:51:27.211 | [34m INFO 82922[0;39m | [1;33mhttp-nio-80-exec-9[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/kepler/integration/share/component/page) 参数({"componentName":"","size":15,"page":1})]
2025-09-04 16:51:27.284 | [34m INFO 82922[0;39m | [1;33mhttp-nio-80-exec-9[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/kepler/integration/share/component/page) 耗时(73 ms)]
2025-09-04 16:51:54.415 | [34m INFO 82922[0;39m | [1;33mhttp-nio-80-exec-10[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/kepler/integration/product/list) 参数({noCache=1756975917960})]
2025-09-04 16:51:54.484 | [34m INFO 82922[0;39m | [1;33mhttp-nio-80-exec-10[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/kepler/integration/product/list) 耗时(68 ms)]
2025-09-04 16:51:58.454 | [34m INFO 82922[0;39m | [1;33mhttp-nio-80-exec-1[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/kepler/integration/share/component/page) 参数({"componentName":"","size":15,"page":1})]
2025-09-04 16:51:58.472 | [34m INFO 82922[0;39m | [1;33mhttp-nio-80-exec-1[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/kepler/integration/share/component/page) 耗时(17 ms)]
2025-09-04 16:52:00.015 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.task.ScaScanDistributedTask       [0;39m | =======执行的SCA状态扫描任务======
2025-09-04 16:52:00.110 | [34m INFO 82922[0;39m | [1;33mscheduling-1[0;39m [1;32mc.t.i.task.ScaScanDistributedTask       [0;39m | []
2025-09-04 16:52:02.585 | [34m INFO 82922[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mc.a.n.client.config.impl.ClientWorker   [0;39m | [fixed-10.180.72.6_8848-dev] [polling-resp] config changed. dataId=kepler-integration.yaml, group=DEFAULT_GROUP, tenant=dev
2025-09-04 16:52:02.585 | [34m INFO 82922[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mc.a.n.client.config.impl.ClientWorker   [0;39m | get changedGroupKeys:[kepler-integration.yaml+DEFAULT_GROUP+dev]
2025-09-04 16:52:02.686 | [34m INFO 82922[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mc.a.n.client.config.impl.ClientWorker   [0;39m | [fixed-10.180.72.6_8848-dev] [data-received] dataId=kepler-integration.yaml, group=DEFAULT_GROUP, tenant=dev, md5=75e153503af175a151aef692c1460506, content=server:
  port: 80
  servlet:
    context-path: /kepler/integration
spring:
  application:
    name:..., type=yaml
2025-09-04 16:52:02.701 | [34m INFO 82922[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mc.a.nacos.client.config.impl.CacheData  [0;39m | [fixed-10.180.72.6_8848-dev] [notify-context] dataId=kepler-integration.yaml, group=DEFAULT_GROUP, md5=75e153503af175a151aef692c1460506
2025-09-04 16:52:02.897 | [34m INFO 82922[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mptablePropertiesBeanFactoryPostProcessor[0;39m | Post-processing PropertySource instances
2025-09-04 16:52:02.902 | [34m INFO 82922[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2025-09-04 16:52:02.902 | [34m INFO 82922[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 16:52:02.902 | [34m INFO 82922[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource refreshArgs [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 16:52:02.902 | [34m INFO 82922[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 16:52:02.902 | [34m INFO 82922[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 16:52:02.902 | [34m INFO 82922[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-09-04 16:52:02.903 | [34m INFO 82922[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-09-04 16:52:02.903 | [34m INFO 82922[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 16:52:02.903 | [34m INFO 82922[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-04 16:52:02.908 | [34m INFO 82922[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mc.u.j.filter.DefaultLazyPropertyFilter  [0;39m | Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-09-04 16:52:02.909 | [34m INFO 82922[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mc.u.j.r.DefaultLazyPropertyResolver     [0;39m | Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-09-04 16:52:02.909 | [34m INFO 82922[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mc.u.j.d.DefaultLazyPropertyDetector     [0;39m | Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-09-04 16:52:02.931 | [31m WARN 82922[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mc.a.c.n.c.NacosPropertySourceBuilder    [0;39m | Ignore the empty nacos configuration and get it based on dataId[kepler-integration] & group[DEFAULT_GROUP]
2025-09-04 16:52:02.953 | [31m WARN 82922[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mc.a.c.n.c.NacosPropertySourceBuilder    [0;39m | Ignore the empty nacos configuration and get it based on dataId[kepler-integration-dev.yaml] & group[DEFAULT_GROUP]
2025-09-04 16:52:02.960 | [31m WARN 82922[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mc.a.c.n.c.NacosPropertySourceBuilder    [0;39m | Ignore the empty nacos configuration and get it based on dataId[kepler-integration-uat.yaml] & group[DEFAULT_GROUP]
2025-09-04 16:52:02.960 | [34m INFO 82922[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mb.c.PropertySourceBootstrapConfiguration[0;39m | Located property source: [BootstrapPropertySource {name='bootstrapProperties-kepler-integration-uat.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kepler-integration-dev.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kepler-integration.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kepler-integration,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-application.yml,DEFAULT_GROUP'}]
2025-09-04 16:52:02.988 | [34m INFO 82922[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mEnableEncryptablePropertiesConfiguration[0;39m | Bootstraping jasypt-string-boot auto configuration in context: kepler-integration-1
2025-09-04 16:52:02.989 | [34m INFO 82922[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mo.s.boot.SpringApplication              [0;39m | The following 2 profiles are active: "dev", "uat"
2025-09-04 16:52:02.999 | [34m INFO 82922[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mo.s.boot.SpringApplication              [0;39m | Started application in 0.26 seconds (JVM running for 435.362)
2025-09-04 16:52:03.189 | [34m INFO 82922[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mu.j.c.RefreshScopeRefreshedEventListener[0;39m | Refreshing cached encryptable property sources
2025-09-04 16:52:03.190 | [34m INFO 82922[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mu.j.c.RefreshScopeRefreshedEventListener[0;39m | Refreshing cached encryptable property sources
2025-09-04 16:52:03.190 | [34m INFO 82922[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mo.s.c.e.event.RefreshEventListener      [0;39m | Refresh keys changed: [spring.redis.password, spring.datasource.username, oss.secret-key, schedule.datashare.system-full-cron, oss.access-key, spring.smart-redisson.password, spring.datasource.password]
2025-09-04 16:52:03.191 | [34m INFO 82922[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mc.a.nacos.client.config.impl.CacheData  [0;39m | [fixed-10.180.72.6_8848-dev] [notify-ok] dataId=kepler-integration.yaml, group=DEFAULT_GROUP, md5=75e153503af175a151aef692c1460506, listener=com.alibaba.cloud.nacos.refresh.NacosContextRefresher$1@70ef05fe 
2025-09-04 16:52:03.191 | [34m INFO 82922[0;39m | [1;33mcom.alibaba.nacos.client.Worker.longPolling.fixed-10.180.72.6_8848-dev[0;39m [1;32mc.a.nacos.client.config.impl.CacheData  [0;39m | [fixed-10.180.72.6_8848-dev] [notify-listener] time cost=490ms in ClientWorker, dataId=kepler-integration.yaml, group=DEFAULT_GROUP, md5=75e153503af175a151aef692c1460506, listener=com.alibaba.cloud.nacos.refresh.NacosContextRefresher$1@70ef05fe 
2025-09-04 16:52:03.619 | [34m INFO 82922[0;39m | [1;33mhttp-nio-80-exec-2[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [preHandle][开始请求 URL(/kepler/integration/product/list) 参数({noCache=1756975929754})]
2025-09-04 16:52:03.678 | [34m INFO 82922[0;39m | [1;33mhttp-nio-80-exec-2[0;39m [1;32mc.t.t.f.a.c.i.ApiAccessLogInterceptor   [0;39m | [afterCompletion][完成请求 URL(/kepler/integration/product/list) 耗时(57 ms)]
2025-09-04 16:52:07.748 | [31m WARN 82922[0;39m | [1;33mThread-16[0;39m [1;32mc.a.nacos.common.notify.NotifyCenter    [0;39m | [NotifyCenter] Start destroying Publisher
2025-09-04 16:52:07.749 | [31m WARN 82922[0;39m | [1;33mThread-16[0;39m [1;32mc.a.nacos.common.notify.NotifyCenter    [0;39m | [NotifyCenter] Destruction of the end
2025-09-04 16:52:07.749 | [31m WARN 82922[0;39m | [1;33mThread-2[0;39m [1;32mc.a.n.common.http.HttpClientBeanHolder  [0;39m | [HttpClientBeanHolder] Start destroying common HttpClient
2025-09-04 16:52:07.750 | [31m WARN 82922[0;39m | [1;33mThread-2[0;39m [1;32mc.a.n.common.http.HttpClientBeanHolder  [0;39m | [HttpClientBeanHolder] Destruction of the end
2025-09-04 16:52:07.780 | [34m INFO 82922[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.a.c.n.registry.NacosServiceRegistry   [0;39m | De-registering from Nacos Server now...
2025-09-04 16:52:07.780 | [34m INFO 82922[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [BEAT] removing beat: DEFAULT_GROUP@@kepler-integration:************:80 from beat map.
2025-09-04 16:52:07.780 | [34m INFO 82922[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [DEREGISTER-SERVICE] dev deregistering service DEFAULT_GROUP@@kepler-integration with instance: Instance{instanceId='null', ip='************', port=80, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={}}
2025-09-04 16:52:07.787 | [34m INFO 82922[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.a.c.n.registry.NacosServiceRegistry   [0;39m | De-registration finished.
2025-09-04 16:52:07.787 | [34m INFO 82922[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown begin
2025-09-04 16:52:10.410 | [34m INFO 82922[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown stop
2025-09-04 16:52:10.410 | [34m INFO 82922[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.core.HostReactor do shutdown begin
2025-09-04 16:52:10.616 | [34m INFO 82922[0;39m | [1;33mcom.alibaba.nacos.client.naming.updater[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | removed ips(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-09-04 16:52:10.619 | [34m INFO 82922[0;39m | [1;33mcom.alibaba.nacos.client.naming.updater[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | current ips:(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"***********#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"***********","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-09-04 16:52:10.620 | [34m INFO 82922[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.core.PushReceiver do shutdown begin
2025-09-04 16:52:13.632 | [34m INFO 82922[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.core.PushReceiver do shutdown stop
2025-09-04 16:52:13.632 | [34m INFO 82922[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin
2025-09-04 16:52:13.632 | [34m INFO 82922[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop
2025-09-04 16:52:13.633 | [34m INFO 82922[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.core.HostReactor do shutdown stop
2025-09-04 16:52:13.633 | [34m INFO 82922[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.net.NamingProxy do shutdown begin
2025-09-04 16:52:13.633 | [31m WARN 82922[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [NamingHttpClientManager] Start destroying NacosRestTemplate
2025-09-04 16:52:13.633 | [31m WARN 82922[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [NamingHttpClientManager] Destruction of the end
2025-09-04 16:52:13.634 | [34m INFO 82922[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.a.n.client.identify.CredentialWatcher [0;39m | [null] CredentialWatcher is stopped
2025-09-04 16:52:13.634 | [34m INFO 82922[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.a.n.client.identify.CredentialService [0;39m | [null] CredentialService is freed
2025-09-04 16:52:13.634 | [34m INFO 82922[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.net.NamingProxy do shutdown stop
2025-09-04 16:52:13.634 | [31m WARN 82922[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mo.s.b.f.support.DisposableBeanAdapter   [0;39m | Custom destroy method 'close' on bean with name 'nacosServiceRegistry' threw an exception: java.lang.NullPointerException
2025-09-04 16:52:13.676 | [34m INFO 82922[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource start closing ....
2025-09-04 16:52:13.678 | [34m INFO 82922[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1} closing ...
2025-09-04 16:52:13.685 | [34m INFO 82922[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1} closed
2025-09-04 16:52:13.685 | [34m INFO 82922[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.b.d.d.d.DefaultDataSourceDestroyer    [0;39m | dynamic-datasource close the datasource named [master] success,
2025-09-04 16:52:13.685 | [34m INFO 82922[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource all closed success,bye
