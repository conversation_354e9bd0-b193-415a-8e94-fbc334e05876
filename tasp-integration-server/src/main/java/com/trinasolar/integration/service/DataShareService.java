package com.trinasolar.integration.service;

import com.trinasolar.integration.api.entity.AppSystem;

/**
 * @className: DataShareService
 * @Description: 数据共享服务
 * @author: pengshy
 * @date: 2025/9/2 16:59
 */
public interface DataShareService {

    void executeDataShareFull();

    void executeDataShareIncrement();

    void executeDataShareProgramFull();

    void executeDataShareProgramIncrement();

    boolean pushAppSystemToKafka(AppSystem appSystem, String triggerUser);
}
