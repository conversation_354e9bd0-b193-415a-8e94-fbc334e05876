package com.trinasolar.integration.service;

import com.trinasolar.integration.api.dto.SystemSyncReqDTO;
import com.trinasolar.integration.api.entity.UserApp;
import com.trinasolar.integration.api.vo.AppGitlabSaveVO;
import com.trinasolar.integration.controller.devops.vo.DevOpsProjectRespVO;
import com.trinasolar.tasc.framework.common.pojo.CommonResult;

import javax.validation.Valid;
import java.util.List;

/**
 * 项目管理 Service 接口
 *
 * <AUTHOR>
 */
public interface ProjectService {

    /**
     * 创建项目管理
     *
     * @param appSystemVO 创建信息
     * @return 编号
     */
    DevOpsProjectRespVO createProject(@Valid AppGitlabSaveVO appSystemVO);

    Boolean syncDownStream(List<SystemSyncReqDTO> systemSyncReqDTOList,Boolean browser);

    CommonResult<Long> createKnowlege(Long systemId);

    List<String> getSystemAdmin(Long systemId);

    List<UserApp> getSystemAdminUser(Long systemId);
}