package com.trinasolar.integration.controller;

import com.trinasolar.integration.api.dto.SystemSyncReqDTO;
import com.trinasolar.integration.service.ProjectConfigService;
import com.trinasolar.integration.service.ProjectService;
import com.trinasolar.tasc.framework.common.pojo.CommonResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

import static com.trinasolar.tasc.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 项目配置信息")
@RestController
@RequestMapping("/project/config")
@Validated
public class ProjectConfigController {

    @Resource
    private ProjectConfigService configService;

    @Resource
    private ProjectService projectService;

    // @TransMethodResult
    @GetMapping("/initCheck")
    @Operation(summary = "查询应用系统是否初始化完成")
    @Parameter(name = "appSystemId", description = "编号", required = true, example = "1024")
    public CommonResult<Boolean> initCheck(@RequestParam("appSystemId") Long appSystemId) {
        Boolean isInit = configService.initCheck(appSystemId);
        return success(isInit);
    }

    @PostMapping("/sync/downStream")
    @Operation(summary = "应用系统同步下游系统")
    public CommonResult<Boolean> syncDownStream(@RequestBody List<SystemSyncReqDTO> systemSyncReqDTOList,
                                                @RequestParam(required = false) Boolean browser) {
        return success(projectService.syncDownStream(systemSyncReqDTOList, browser));
    }

    @PutMapping("/createKnowlege")
    @Operation(summary = "初始化应用系统文档库")
    public CommonResult<Long> createKnowlege(@RequestParam(value = "systemId") Long systemId) {
        return projectService.createKnowlege(systemId);
    }

    @GetMapping("/getSystemAdministrator")
    @Operation(summary = "获取应用系统管理员")
    public CommonResult<List<String>> getSystemAdministrator(@RequestParam(value = "systemId") Long systemId) {
        return success(projectService.getSystemAdmin(systemId));
    }

}